package com.grain.risk.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 节假日配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("holiday_config")
public class HolidayConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 节假日日期
     */
    @TableField("holiday_date")
    private LocalDate holidayDate;

    /**
     * 节假日名称
     */
    @TableField("holiday_name")
    private String holidayName;

    /**
     * 节假日类型：1-法定节假日，2-调休，3-自定义休息日
     */
    @TableField("holiday_type")
    private Integer holidayType;

    /**
     * 是否为工作日：0-休息日，1-工作日（用于调休）
     */
    @TableField("is_workday")
    private Integer isWorkday;

    /**
     * 年份
     */
    @TableField("year_num")
    private Integer yearNum;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("is_deleted")
    private Integer isDeleted;
}