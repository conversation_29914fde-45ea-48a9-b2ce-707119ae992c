<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板切换管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .current-template {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .current-template h2 {
            color: #155724;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .template-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .info-item h4 {
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .info-item p {
            color: #6c757d;
            font-size: 13px;
        }
        
        .templates-section {
            margin-bottom: 30px;
        }
        
        .templates-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .template-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .template-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .template-card.selected {
            border-color: #007bff;
            background: #f8f9fa;
            box-shadow: 0 8px 25px rgba(0,123,255,0.2);
        }
        
        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .template-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }
        
        .template-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .template-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .badge-strict {
            background: #f8d7da;
            color: #721c24;
        }
        
        .badge-standard {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .badge-simple {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-custom {
            background: #e2e3e5;
            color: #383d41;
        }
        
        .template-details {
            margin-bottom: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .detail-label {
            color: #6c757d;
        }
        
        .detail-value {
            color: #2c3e50;
            font-weight: 500;
        }
        
        .template-description {
            color: #6c757d;
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 15px;
        }
        
        .template-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            border: none;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 1px solid #6c757d;
        }
        
        .btn-outline:hover {
            background: #6c757d;
            color: white;
        }
        
        .switch-panel {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
            display: none;
        }
        
        .switch-panel.active {
            display: block;
        }
        
        .switch-panel h3 {
            color: #856404;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .impact-assessment {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .impact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .impact-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .impact-value {
            font-size: 24px;
            font-weight: bold;
            color: #856404;
            margin-bottom: 5px;
        }
        
        .impact-label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .switch-settings {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .setting-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
        }
        
        .setting-group h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .radio-item input[type="radio"] {
            margin: 0;
        }
        
        .radio-item label {
            font-size: 13px;
            color: #2c3e50;
            cursor: pointer;
        }
        
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin: 0;
        }
        
        .checkbox-item label {
            font-size: 13px;
            color: #2c3e50;
            cursor: pointer;
        }
        
        .switch-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            font-size: 16px;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .back-button {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 模板切换管理</h1>
            <p>一键切换不同复杂度的作业模板</p>
        </div>
        
        <div class="main-content">
            <div class="back-button">
                <a href="配置功能主界面.html" class="btn btn-secondary">
                    ← 返回主界面
                </a>
            </div>
            
            <!-- 当前使用模板 -->
            <div class="current-template">
                <h2>
                    <span class="section-icon">✅</span>
                    当前使用模板
                </h2>
                
                <div class="template-info">
                    <div class="info-item">
                        <h4>模板名称</h4>
                        <p>动火作业 - 标准版模板A + 定制扩展</p>
                    </div>
                    <div class="info-item">
                        <h4>启用时间</h4>
                        <p>2024-01-15 08:00</p>
                    </div>
                    <div class="info-item">
                        <h4>使用统计</h4>
                        <p>本月156次申请</p>
                    </div>
                    <div class="info-item">
                        <h4>平均处理时间</h4>
                        <p>1.8小时</p>
                    </div>
                    <div class="info-item">
                        <h4>涉及人员</h4>
                        <p>23人</p>
                    </div>
                    <div class="info-item">
                        <h4>满意度</h4>
                        <p>92%</p>
                    </div>
                </div>
            </div>
            
            <!-- 可切换模板选项 -->
            <div class="templates-section">
                <h2>
                    <span class="section-icon">📋</span>
                    可切换模板选项
                </h2>
                
                <div class="templates-grid">
                    <div class="template-card" onclick="selectTemplate(this, 'strict')">
                        <div class="template-header">
                            <div class="template-title">
                                <span class="template-icon">🔥</span>
                                严格版模板B
                            </div>
                            <span class="template-badge badge-strict">高复杂度</span>
                        </div>
                        
                        <div class="template-details">
                            <div class="detail-row">
                                <span class="detail-label">流程节点：</span>
                                <span class="detail-value">9个</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">表单字段：</span>
                                <span class="detail-value">18个</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">处理时间：</span>
                                <span class="detail-value">3-5小时</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">适用场景：</span>
                                <span class="detail-value">大型库点</span>
                            </div>
                        </div>
                        
                        <div class="template-description">
                            更严格的审批流程，包含总经理审批、环保审批、现场检查、气体检测等多个环节，适用于高风险作业场景。
                        </div>
                        
                        <div class="template-actions">
                            <button class="btn btn-primary">选择此模板</button>
                            <button class="btn btn-outline">预览详情</button>
                        </div>
                    </div>
                    
                    <div class="template-card" onclick="selectTemplate(this, 'simple')">
                        <div class="template-header">
                            <div class="template-title">
                                <span class="template-icon">🔥</span>
                                简化版模板C
                            </div>
                            <span class="template-badge badge-simple">低复杂度</span>
                        </div>
                        
                        <div class="template-details">
                            <div class="detail-row">
                                <span class="detail-label">流程节点：</span>
                                <span class="detail-value">4个</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">表单字段：</span>
                                <span class="detail-value">8个</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">处理时间：</span>
                                <span class="detail-value">0.5-1小时</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">适用场景：</span>
                                <span class="detail-value">小型库点</span>
                            </div>
                        </div>
                        
                        <div class="template-description">
                            简化的快速流程，只包含必要的审批环节，适用于低风险作业或小型库点的快速处理需求。
                        </div>
                        
                        <div class="template-actions">
                            <button class="btn btn-primary">选择此模板</button>
                            <button class="btn btn-outline">预览详情</button>
                        </div>
                    </div>
                    
                    <div class="template-card" onclick="selectTemplate(this, 'standard')">
                        <div class="template-header">
                            <div class="template-title">
                                <span class="template-icon">🔥</span>
                                标准版模板A
                            </div>
                            <span class="template-badge badge-standard">中复杂度</span>
                        </div>
                        
                        <div class="template-details">
                            <div class="detail-row">
                                <span class="detail-label">流程节点：</span>
                                <span class="detail-value">5个</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">表单字段：</span>
                                <span class="detail-value">12个</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">处理时间：</span>
                                <span class="detail-value">2-4小时</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">适用场景：</span>
                                <span class="detail-value">中型库点</span>
                            </div>
                        </div>
                        
                        <div class="template-description">
                            标准的作业流程，平衡了安全要求和处理效率，适用于大多数中型库点的日常作业需求。
                        </div>
                        
                        <div class="template-actions">
                            <button class="btn btn-primary">选择此模板</button>
                            <button class="btn btn-outline">预览详情</button>
                        </div>
                    </div>
                    
                    <div class="template-card" onclick="selectTemplate(this, 'custom')">
                        <div class="template-header">
                            <div class="template-title">
                                <span class="template-icon">🔥</span>
                                自定义模板
                            </div>
                            <span class="template-badge badge-custom">可定制</span>
                        </div>
                        
                        <div class="template-details">
                            <div class="detail-row">
                                <span class="detail-label">流程节点：</span>
                                <span class="detail-value">可调整</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">表单字段：</span>
                                <span class="detail-value">可调整</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">处理时间：</span>
                                <span class="detail-value">可调整</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">适用场景：</span>
                                <span class="detail-value">特殊需求</span>
                            </div>
                        </div>
                        
                        <div class="template-description">
                            基于现有模板进行深度定制，可以根据特殊业务需求调整流程节点、表单字段和业务规则。
                        </div>
                        
                        <div class="template-actions">
                            <button class="btn btn-primary">开始定制</button>
                            <button class="btn btn-outline">查看示例</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 切换操作面板 -->
            <div class="switch-panel" id="switchPanel">
                <h3>
                    <span class="section-icon">⚠️</span>
                    模板切换确认
                </h3>
                
                <div class="impact-assessment">
                    <h4 style="margin-bottom: 15px;">影响评估报告</h4>
                    <div class="impact-grid">
                        <div class="impact-item">
                            <div class="impact-value">3</div>
                            <div class="impact-label">进行中流程</div>
                        </div>
                        <div class="impact-item">
                            <div class="impact-value">23</div>
                            <div class="impact-label">需要通知人员</div>
                        </div>
                        <div class="impact-item">
                            <div class="impact-value">5分钟</div>
                            <div class="impact-label">预计切换耗时</div>
                        </div>
                        <div class="impact-item">
                            <div class="impact-value">今晚22:00</div>
                            <div class="impact-label">建议切换时间</div>
                        </div>
                    </div>
                </div>
                
                <div class="switch-settings">
                    <div class="setting-group">
                        <h4>切换时间设置</h4>
                        <div class="radio-group">
                            <div class="radio-item">
                                <input type="radio" id="immediate-switch" name="switch-timing">
                                <label for="immediate-switch">⚡ 立即切换</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="scheduled-switch" name="switch-timing" checked>
                                <label for="scheduled-switch">📅 定时切换：今晚22:00</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-group">
                        <h4>通知设置</h4>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="sms-notify" checked>
                                <label for="sms-notify">📱 短信通知</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="system-notify" checked>
                                <label for="system-notify">💬 系统消息</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="email-notify">
                                <label for="email-notify">📧 邮件通知</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="switch-actions">
                    <button class="btn btn-secondary" onclick="cancelSwitch()">
                        取消切换
                    </button>
                    <button class="btn btn-success" onclick="confirmSwitch()">
                        🚀 确认切换
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let selectedTemplate = null;
        
        function selectTemplate(element, templateType) {
            // 移除其他模板的选中状态
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 添加当前模板的选中状态
            element.classList.add('selected');
            selectedTemplate = templateType;
            
            // 显示切换面板
            document.getElementById('switchPanel').classList.add('active');
        }
        
        function cancelSwitch() {
            // 隐藏切换面板
            document.getElementById('switchPanel').classList.remove('active');
            
            // 移除所有模板的选中状态
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            selectedTemplate = null;
        }
        
        function confirmSwitch() {
            if (!selectedTemplate) {
                alert('请先选择要切换的模板');
                return;
            }
            
            const templateNames = {
                'strict': '严格版模板B',
                'simple': '简化版模板C',
                'standard': '标准版模板A',
                'custom': '自定义模板'
            };
            
            const templateName = templateNames[selectedTemplate];
            
            if (confirm(`确认要切换到"${templateName}"吗？\n\n切换后：\n• 进行中的流程将继续使用旧模板\n• 新的申请将使用新模板\n• 相关人员将收到通知`)) {
                // 模拟切换过程
                alert(`模板切换成功！\n\n✅ 已切换到：${templateName}\n📧 相关人员已收到通知\n📚 新流程培训材料已发送\n\n新模板将在今晚22:00生效。`);
                
                // 刷新页面或跳转
                window.location.reload();
            }
        }
    </script>
</body>
</html>
