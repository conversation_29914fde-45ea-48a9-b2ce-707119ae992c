<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>环流熏蒸作业 - 移动端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f7f8fa;
            color: #333;
            line-height: 1.4;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-title {
            font-size: 18px;
            font-weight: 600;
            text-align: center;
        }
        
        .header-subtitle {
            font-size: 12px;
            text-align: center;
            opacity: 0.9;
            margin-top: 4px;
        }
        
        .container {
            padding: 15px;
        }
        
        .workflow-progress {
            background: white;
            border-radius: 12px;
            padding: 20px 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .progress-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .progress-steps {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .progress-step {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            background: #f8f9fa;
            position: relative;
        }
        
        .progress-step.completed {
            background: #e8f5e8;
            border-left: 4px solid #4CAF50;
        }
        
        .progress-step.active {
            background: #e3f2fd;
            border-left: 4px solid #2196F3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
        }
        
        .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            background: #ddd;
            color: #666;
        }
        
        .progress-step.completed .step-icon {
            background: #4CAF50;
            color: white;
        }
        
        .progress-step.active .step-icon {
            background: #2196F3;
            color: white;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .step-time {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .form-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .form-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 8px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #555;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            background: #fff;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }
        
        .form-input:read-only {
            background: #f8f9fa;
            color: #666;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .camera-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px 20px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .camera-upload:active {
            background: #f0f0f0;
            transform: scale(0.98);
        }
        
        .camera-icon {
            font-size: 32px;
            color: #999;
            margin-bottom: 8px;
        }
        
        .camera-text {
            font-size: 14px;
            color: #666;
        }
        
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 12px;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:active {
            background: #45a049;
            transform: scale(0.98);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-outline {
            background: transparent;
            color: #4CAF50;
            border: 1px solid #4CAF50;
        }
        
        .monitor-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .monitor-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .monitor-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .monitor-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            border-left: 4px solid #4CAF50;
        }
        
        .monitor-item.alert {
            border-left-color: #f44336;
            background: #fff5f5;
        }
        
        .monitor-value {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }
        
        .monitor-item.alert .monitor-value {
            color: #f44336;
        }
        
        .monitor-label {
            font-size: 12px;
            color: #666;
        }
        
        .update-time {
            text-align: center;
            font-size: 12px;
            color: #999;
            margin-top: 10px;
        }
        
        .floating-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: #4CAF50;
            color: white;
            border: none;
            font-size: 24px;
            box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .floating-btn:active {
            transform: scale(0.9);
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .info-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
        }
        
        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">环流熏蒸作业</div>
        <div class="header-subtitle">FUM-2024-001 | 张三（作业负责人）</div>
    </div>
    
    <div class="container">
        <!-- 流程进度 -->
        <div class="workflow-progress">
            <div class="progress-title">作业流程进度</div>
            <div class="progress-steps">
                <div class="progress-step completed">
                    <div class="step-icon">✓</div>
                    <div class="step-content">
                        <div class="step-title">熏蒸方案制定</div>
                        <div class="step-time">2024-01-15 10:30 完成</div>
                    </div>
                </div>
                <div class="progress-step completed">
                    <div class="step-icon">✓</div>
                    <div class="step-content">
                        <div class="step-title">作业前准备</div>
                        <div class="step-time">2024-01-16 08:00 完成</div>
                    </div>
                </div>
                <div class="progress-step active">
                    <div class="step-icon">3</div>
                    <div class="step-content">
                        <div class="step-title">施药操作</div>
                        <div class="step-time">进行中...</div>
                    </div>
                </div>
                <div class="progress-step">
                    <div class="step-icon">4</div>
                    <div class="step-content">
                        <div class="step-title">补药操作</div>
                        <div class="step-time">待执行</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="form-card">
            <div class="form-title">作业基本信息</div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">申请编号</div>
                    <div class="info-value">FUM-2024-001</div>
                </div>
                <div class="info-item">
                    <div class="info-label">仓房编号</div>
                    <div class="info-value">1号仓</div>
                </div>
                <div class="info-item">
                    <div class="info-label">粮食品种</div>
                    <div class="info-value">小麦</div>
                </div>
                <div class="info-item">
                    <div class="info-label">审批状态</div>
                    <div class="info-value">
                        <span class="status-badge status-approved">已审批</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 施药操作表单 -->
        <div class="form-card">
            <div class="form-title">施药操作记录</div>
            
            <div class="form-group">
                <label class="form-label">熏蒸剂类型</label>
                <select class="form-input">
                    <option value="磷化铝">磷化铝</option>
                    <option value="磷化镁">磷化镁</option>
                </select>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">计划用量(kg)</label>
                    <input type="text" class="form-input" value="15.5" readonly>
                </div>
                <div class="form-group">
                    <label class="form-label">实际用量(kg)</label>
                    <input type="number" class="form-input" placeholder="请输入" step="0.1">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">施药时间</label>
                <input type="datetime-local" class="form-input">
            </div>
            
            <div class="form-group">
                <label class="form-label">施药方式</label>
                <select class="form-input">
                    <option value="环流施药">环流施药</option>
                    <option value="直接投药">直接投药</option>
                    <option value="管道施药">管道施药</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label">现场拍照</label>
                <div class="camera-upload" onclick="takePhoto()">
                    <div class="camera-icon">📷</div>
                    <div class="camera-text">点击拍照记录</div>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">操作人员</label>
                    <input type="text" class="form-input" placeholder="请输入姓名">
                </div>
                <div class="form-group">
                    <label class="form-label">监督人员</label>
                    <input type="text" class="form-input" placeholder="请输入姓名">
                </div>
            </div>
            
            <button class="btn btn-primary" onclick="submitForm()">提交记录</button>
            <button class="btn btn-outline" onclick="saveDraft()">保存草稿</button>
        </div>
        
        <!-- 实时监测 -->
        <div class="monitor-card">
            <div class="monitor-title">实时监测数据</div>
            <div class="monitor-grid">
                <div class="monitor-item">
                    <div class="monitor-value" id="temp">23.5°C</div>
                    <div class="monitor-label">仓内温度</div>
                </div>
                <div class="monitor-item">
                    <div class="monitor-value">65%</div>
                    <div class="monitor-label">相对湿度</div>
                </div>
                <div class="monitor-item alert">
                    <div class="monitor-value" id="ph3">850</div>
                    <div class="monitor-label">PH3浓度(mg/m³)</div>
                </div>
                <div class="monitor-item">
                    <div class="monitor-value">18.5%</div>
                    <div class="monitor-label">氧气浓度</div>
                </div>
            </div>
            <div class="update-time" id="updateTime">最后更新：2024-01-16 14:30:25</div>
        </div>
    </div>
    
    <!-- 浮动按钮 -->
    <button class="floating-btn" onclick="showMenu()">⋯</button>
    
    <script>
        // 拍照功能
        function takePhoto() {
            // 模拟拍照功能
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.capture = 'camera';
            input.click();
            
            input.addEventListener('change', function() {
                if (input.files.length > 0) {
                    const uploadArea = document.querySelector('.camera-upload');
                    uploadArea.innerHTML = `
                        <div class="camera-icon">✓</div>
                        <div class="camera-text">已拍摄 ${input.files.length} 张照片</div>
                    `;
                    uploadArea.style.borderColor = '#4CAF50';
                    uploadArea.style.background = '#f0f8f0';
                }
            });
        }
        
        // 提交表单
        function submitForm() {
            // 简单验证
            const requiredFields = document.querySelectorAll('.form-input[placeholder*="请输入"]');
            let allFilled = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    allFilled = false;
                    field.style.borderColor = '#f44336';
                } else {
                    field.style.borderColor = '#e1e5e9';
                }
            });
            
            if (allFilled) {
                alert('记录提交成功！');
            } else {
                alert('请填写所有必填项');
            }
        }
        
        // 保存草稿
        function saveDraft() {
            alert('草稿已保存');
        }
        
        // 显示菜单
        function showMenu() {
            const actions = ['查看历史记录', '导出数据', '联系技术支持', '返回首页'];
            const action = prompt('请选择操作：\n' + actions.map((item, index) => `${index + 1}. ${item}`).join('\n'));
            if (action) {
                alert(`执行操作：${actions[parseInt(action) - 1] || '无效选择'}`);
            }
        }
        
        // 模拟实时数据更新
        setInterval(function() {
            const tempElement = document.getElementById('temp');
            const ph3Element = document.getElementById('ph3');
            const timeElement = document.getElementById('updateTime');
            
            // 更新温度
            const currentTemp = parseFloat(tempElement.textContent);
            const newTemp = (currentTemp + (Math.random() - 0.5) * 0.2).toFixed(1);
            tempElement.textContent = newTemp + '°C';
            
            // 更新PH3浓度
            const currentPh3 = parseInt(ph3Element.textContent);
            const newPh3 = Math.max(800, currentPh3 + Math.floor((Math.random() - 0.5) * 20));
            ph3Element.textContent = newPh3;
            
            // 更新时间
            const now = new Date();
            const timeStr = `${now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')} ${String(now.getHours()).padStart(2,'0')}:${String(now.getMinutes()).padStart(2,'0')}:${String(now.getSeconds()).padStart(2,'0')}`;
            timeElement.textContent = `最后更新：${timeStr}`;
        }, 3000);
        
        // 防止页面缩放
        document.addEventListener('touchstart', function(event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        });
        
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
