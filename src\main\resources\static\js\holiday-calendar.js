/**
 * 日历视图节假日管理系统 JavaScript
 */

// 全局变量
let currentYear = new Date().getFullYear();
let holidayData = new Map(); // 存储节假日数据
let publicHolidayAPI = 'https://timor.tech/api/holiday/year/'; // 公共节假日API

// API基础路径
const API_BASE_URL = '/grain-risk/api/holidays';

// 月份名称
const monthNames = ['一月', '二月', '三月', '四月', '五月', '六月',
                   '七月', '八月', '九月', '十月', '十一月', '十二月'];

// 星期名称
const weekdayNames = ['日', '一', '二', '三', '四', '五', '六'];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 初始化年份选择器
    initYearSelector();

    // 加载节假日数据
    loadHolidayData();

    // 绑定事件监听器
    bindEventListeners();
}

/**
 * 初始化年份选择器
 */
function initYearSelector() {
    const yearSelector = document.getElementById('yearSelector');
    const fetchYearSelector = document.getElementById('fetchYear');

    // 清空现有选项
    yearSelector.innerHTML = '';
    if (fetchYearSelector) {
        fetchYearSelector.innerHTML = '';
    }

    // 添加年份选项（当前年份前后5年）
    for (let year = currentYear - 2; year <= currentYear + 5; year++) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year + '年';
        if (year === currentYear) {
            option.selected = true;
        }
        yearSelector.appendChild(option);

        if (fetchYearSelector) {
            const fetchOption = option.cloneNode(true);
            fetchYearSelector.appendChild(fetchOption);
        }
    }
}

/**
 * 生成年历
 */
function generateYearCalendar() {
    const container = document.getElementById('yearCalendar');
    container.innerHTML = '';

    // 更新标题
    document.getElementById('calendarTitle').textContent = `${currentYear}年节假日日历`;

    // 生成12个月的日历
    for (let month = 0; month < 12; month++) {
        const monthCalendar = createMonthCalendar(currentYear, month);
        container.appendChild(monthCalendar);
    }
}

/**
 * 创建单月日历
 */
function createMonthCalendar(year, month) {
    const monthDiv = document.createElement('div');
    monthDiv.className = 'month-calendar';

    // 月份标题
    const monthHeader = document.createElement('div');
    monthHeader.className = 'month-header';
    monthHeader.textContent = monthNames[month];
    monthDiv.appendChild(monthHeader);

    // 日历网格
    const calendarGrid = document.createElement('div');
    calendarGrid.className = 'calendar-grid';

    // 添加星期标题
    weekdayNames.forEach(day => {
        const weekdayHeader = document.createElement('div');
        weekdayHeader.className = 'weekday-header';
        weekdayHeader.textContent = day;
        calendarGrid.appendChild(weekdayHeader);
    });

    // 获取月份信息
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startWeekday = firstDay.getDay();

    // 添加上个月的日期（填充）
    const prevMonth = month === 0 ? 11 : month - 1;
    const prevYear = month === 0 ? year - 1 : year;
    const prevMonthLastDay = new Date(prevYear, prevMonth + 1, 0).getDate();

    for (let i = startWeekday - 1; i >= 0; i--) {
        const dayDiv = createDayElement(prevYear, prevMonth, prevMonthLastDay - i, true);
        calendarGrid.appendChild(dayDiv);
    }

    // 添加当月的日期
    for (let day = 1; day <= daysInMonth; day++) {
        const dayDiv = createDayElement(year, month, day, false);
        calendarGrid.appendChild(dayDiv);
    }

    // 添加下个月的日期（填充）
    const totalCells = calendarGrid.children.length - 7; // 减去星期标题
    const remainingCells = 42 - totalCells; // 6行 * 7列 = 42
    const nextMonth = month === 11 ? 0 : month + 1;
    const nextYear = month === 11 ? year + 1 : year;

    for (let day = 1; day <= remainingCells; day++) {
        const dayDiv = createDayElement(nextYear, nextMonth, day, true);
        calendarGrid.appendChild(dayDiv);
    }

    monthDiv.appendChild(calendarGrid);
    return monthDiv;
}

/**
 * 创建日期元素
 */
function createDayElement(year, month, day, isOtherMonth) {
    const date = new Date(year, month, day);
    const dateStr = formatDateToString(date);
    const today = new Date();
    const isToday = date.toDateString() === today.toDateString();

    const dayDiv = document.createElement('div');
    dayDiv.className = 'calendar-day';
    dayDiv.onclick = () => showDateDetail(date);

    // 添加日期数字
    const dayNumber = document.createElement('div');
    dayNumber.className = 'day-number';
    dayNumber.textContent = day;
    dayDiv.appendChild(dayNumber);

    // 添加日期标签
    const dayLabel = document.createElement('div');
    dayLabel.className = 'day-label';
    dayDiv.appendChild(dayLabel);

    // 设置样式和标签
    if (isOtherMonth) {
        dayDiv.classList.add('other-month');
        dayLabel.textContent = '';
    } else {
        const holiday = holidayData.get(dateStr);
        const isWeekend = date.getDay() === 0 || date.getDay() === 6;

        if (holiday) {
            // 节假日
            switch (holiday.holidayType) {
                case 1:
                    dayDiv.classList.add('legal-holiday');
                    dayLabel.textContent = holiday.holidayName || '节假日';
                    break;
                case 2:
                    dayDiv.classList.add('adjust-workday');
                    dayLabel.textContent = '调休';
                    break;
                case 3:
                    dayDiv.classList.add('custom-holiday');
                    dayLabel.textContent = holiday.holidayName || '休息日';
                    break;
            }
        } else if (isWeekend) {
            // 双休日
            dayDiv.classList.add('weekend');
            dayLabel.textContent = '休息';
        } else {
            // 工作日
            dayDiv.classList.add('workday');
            dayLabel.textContent = '工作日';
        }

        if (isToday) {
            dayDiv.classList.add('today');
        }
    }

    return dayDiv;
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 年份选择器变化事件
    document.getElementById('yearSelector').addEventListener('change', changeYear);
}

/**
 * 加载节假日数据
 */
async function loadHolidayData() {
    try {
        showLoading();

        const response = await axios.get(`${API_BASE_URL}?year=${currentYear}&size=1000`);
        const data = response.data;

        if (data.success && data.data.records) {
            // 清空现有数据
            holidayData.clear();

            // 处理节假日数据
            data.data.records.forEach(holiday => {
                holidayData.set(holiday.holidayDate, holiday);
            });

            // 生成年历
            generateYearCalendar();

            // 更新统计信息
            updateStatistics();
        } else {
            // 如果没有数据，仍然生成日历
            generateYearCalendar();
            updateStatistics();
        }
    } catch (error) {
        console.error('加载节假日数据失败:', error);
        showError('加载节假日数据失败');
        // 即使加载失败，也生成基础日历
        generateYearCalendar();
        updateStatistics();
    } finally {
        hideLoading();
    }
}

/**
 * 显示日期详情
 */
function showDateDetail(date, dayEl) {
    const dateStr = formatDateToString(date);
    const holiday = holidayData.get(dateStr);

    // 填充基本信息
    document.getElementById('modalDate').textContent = formatDateToDisplay(date);
    document.getElementById('modalWeekday').textContent = getWeekdayName(date);
    document.getElementById('modalDayType').textContent = getDayTypeName(date, holiday);
    document.getElementById('modalDateValue').value = dateStr;

    // 填充节假日设置
    if (holiday) {
        document.getElementById('modalHolidayType').value = holiday.holidayType || '';
        document.getElementById('modalHolidayName').value = holiday.holidayName || '';
        document.getElementById('modalRemark').value = holiday.remark || '';
        document.getElementById('removeHolidayBtn').style.display = 'inline-block';
    } else {
        document.getElementById('modalHolidayType').value = '';
        document.getElementById('modalHolidayName').value = '';
        document.getElementById('modalRemark').value = '';
        document.getElementById('removeHolidayBtn').style.display = 'none';
    }

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('dateDetailModal'));
    modal.show();
}

/**
 * 保存节假日设置
 */
async function saveHolidaySetting() {
    try {
        const dateStr = document.getElementById('modalDateValue').value;
        const holidayType = document.getElementById('modalHolidayType').value;
        const holidayName = document.getElementById('modalHolidayName').value;
        const remark = document.getElementById('modalRemark').value;

        if (holidayType && !holidayName.trim()) {
            showError('请输入节假日名称');
            return;
        }

        const holidayData = {
            holidayDate: dateStr,
            holidayName: holidayName.trim(),
            holidayType: holidayType ? parseInt(holidayType) : null,
            isWorkday: holidayType === '2' ? 1 : 0, // 调休为工作日
            yearNum: parseInt(dateStr.split('-')[0]),
            remark: remark.trim()
        };

        const response = await axios.post(API_BASE_URL, holidayData);
        const data = response.data;

        if (data.success) {
            showSuccess('保存成功');
            const modal = bootstrap.Modal.getInstance(document.getElementById('dateDetailModal'));
            modal.hide();

            // 更新本地数据
            if (holidayType) {
                holidayData.set(dateStr, holidayData);
            } else {
                holidayData.delete(dateStr);
            }

            // 重新生成日历
            generateYearCalendar();
            updateStatistics();
        } else {
            showError('保存失败：' + data.message);
        }
    } catch (error) {
        console.error('保存节假日设置失败:', error);
        showError('保存失败');
    }
}

/**
 * 移除节假日
 */
async function removeHoliday() {
    if (!confirm('确定要移除这个节假日设置吗？')) {
        return;
    }

    try {
        const dateStr = document.getElementById('modalDateValue').value;
        const holiday = holidayData.get(dateStr);

        if (holiday && holiday.id) {
            const response = await axios.delete(`${API_BASE_URL}/${holiday.id}`);
            const data = response.data;

            if (data.success) {
                showSuccess('移除成功');
                const modal = bootstrap.Modal.getInstance(document.getElementById('dateDetailModal'));
                modal.hide();

                // 更新本地数据
                holidayData.delete(dateStr);

                // 重新生成日历
                generateYearCalendar();
                updateStatistics();
            } else {
                showError('移除失败：' + data.message);
            }
        }
    } catch (error) {
        console.error('移除节假日失败:', error);
        showError('移除失败');
    }
}

/**
 * 获取公共节假日
 */
function fetchPublicHolidays() {
    const modal = new bootstrap.Modal(document.getElementById('fetchHolidaysModal'));
    modal.show();
}

/**
 * 确认获取公共节假日
 */
async function confirmFetchHolidays() {
    try {
        const year = document.getElementById('fetchYear').value;
        const overwrite = document.getElementById('overwriteExisting').checked;

        showLoading();

        // 调用公共API获取节假日数据
        const response = await axios.get(`${publicHolidayAPI}${year}`);
        const publicData = response.data;

        if (publicData.code === 0 && publicData.holiday) {
            const holidays = Object.entries(publicData.holiday).map(([date, info]) => ({
                holidayDate: date,
                holidayName: info.name,
                holidayType: 1, // 法定节假日
                isWorkday: info.wage === 3 ? 1 : 0, // wage=3表示调休工作日
                yearNum: parseInt(year),
                remark: `从公共API获取：${info.name}`
            }));

            // 批量保存到后端
            const saveResponse = await axios.post(`${API_BASE_URL}/batch`, {
                holidays: holidays,
                overwrite: overwrite
            });

            if (saveResponse.data.success) {
                showSuccess(`成功获取并保存了${holidays.length}个节假日`);
                const modal = bootstrap.Modal.getInstance(document.getElementById('fetchHolidaysModal'));
                modal.hide();

                // 重新加载数据
                await loadHolidayData();
            } else {
                showError('保存节假日数据失败：' + saveResponse.data.message);
            }
        } else {
            showError('获取公共节假日数据失败');
        }
    } catch (error) {
        console.error('获取公共节假日失败:', error);
        showError('获取公共节假日失败，请检查网络连接');
    } finally {
        hideLoading();
    }
}

/**
 * 批量导入节假日
 */
function importHolidays() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

/**
 * 上传文件
 */
async function uploadFile() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];

    if (!file) {
        showError('请选择要导入的文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await axios.post(`${API_BASE_URL}/import`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });

        const data = response.data;
        if (data.success) {
            showSuccess(`导入成功，共导入${data.data}条记录`);
            const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
            modal.hide();
            await loadHolidayData();
        } else {
            showError('导入失败：' + data.message);
        }
    } catch (error) {
        console.error('导入失败:', error);
        showError('导入失败，请检查文件格式');
    }
}

/**
 * 导出节假日数据
 */
async function exportHolidays() {
    try {
        const response = await axios.get(`${API_BASE_URL}/export?year=${currentYear}`, {
            responseType: 'blob'
        });

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `节假日数据_${currentYear}.xlsx`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        showSuccess('导出成功');
    } catch (error) {
        console.error('导出失败:', error);
        showError('导出失败');
    }
}

/**
 * 重置日历
 */
function resetCalendar() {
    if (!confirm('确定要重置日历吗？这将清除所有自定义节假日设置。')) {
        return;
    }

    holidayData.clear();
    generateYearCalendar();
    updateStatistics();
    showSuccess('日历已重置');
}

/**
 * 改变年份
 */
function changeYear() {
    const selectedYear = parseInt(document.getElementById('yearSelector').value);
    if (selectedYear !== currentYear) {
        currentYear = selectedYear;
        loadHolidayData();
    }
}

/**
 * 刷新日历
 */
function refreshCalendar() {
    loadHolidayData();
}

/**
 * 显示当前月份
 */
function showCurrentMonth() {
    const today = new Date();
    currentYear = today.getFullYear();
    document.getElementById('yearSelector').value = currentYear;
    loadHolidayData();
}

/**
 * 更新统计信息
 */
function updateStatistics() {
    const year = currentYear;
    const totalDays = isLeapYear(year) ? 366 : 365;

    let workDays = 0;
    let weekendDays = 0;
    let holidayDays = 0;

    // 遍历一年中的每一天
    for (let month = 0; month < 12; month++) {
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            const dateStr = formatDateToString(date);
            const holiday = holidayData.get(dateStr);
            const isWeekend = date.getDay() === 0 || date.getDay() === 6;

            if (holiday) {
                if (holiday.isWorkday === 1) {
                    workDays++; // 调休工作日
                } else {
                    holidayDays++; // 节假日
                }
            } else if (isWeekend) {
                weekendDays++; // 双休日
            } else {
                workDays++; // 普通工作日
            }
        }
    }

    // 更新显示
    document.getElementById('totalDays').textContent = totalDays;
    document.getElementById('workDays').textContent = workDays;
    document.getElementById('weekendDays').textContent = weekendDays;
    document.getElementById('holidayDays').textContent = holidayDays;
}

/**
 * 格式化日期为字符串
 */
function formatDateToString(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 格式化日期为显示格式
 */
function formatDateToDisplay(date) {
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

/**
 * 获取星期名称
 */
function getWeekdayName(date) {
    const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return weekdays[date.getDay()];
}

/**
 * 获取日期类型名称
 */
function getDayTypeName(date, holiday) {
    if (holiday) {
        const typeNames = {
            1: '法定节假日',
            2: '调休工作日',
            3: '自定义休息日'
        };
        return typeNames[holiday.holidayType] || '节假日';
    } else if (date.getDay() === 0 || date.getDay() === 6) {
        return '双休日';
    } else {
        return '工作日';
    }
}

/**
 * 判断是否为闰年
 */
function isLeapYear(year) {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

/**
 * 下载导入模板
 */
function downloadTemplate() {
    const link = document.createElement('a');
    link.href = '/grain-risk/template/holiday_import_template.xlsx';
    link.setAttribute('download', '节假日导入模板.xlsx');
    document.body.appendChild(link);
    link.click();
    link.remove();
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
    showMessage(message, 'success');
}

/**
 * 显示错误消息
 */
function showError(message) {
    showMessage(message, 'danger');
}

/**
 * 显示信息消息
 */
function showInfo(message) {
    showMessage(message, 'info');
}

/**
 * 显示消息
 */
function showMessage(message, type) {
    // 移除现有的消息
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // 创建新的消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';

    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv && alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

/**
 * 显示加载状态
 */
function showLoading() {
    const calendarEl = document.getElementById('calendar');
    const loadingEl = document.createElement('div');
    loadingEl.id = 'loading-overlay';
    loadingEl.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    `;
    loadingEl.innerHTML = `
        <div class="text-center">
            <div class="loading me-2"></div>
            <div>正在加载数据...</div>
        </div>
    `;

    calendarEl.style.position = 'relative';
    calendarEl.appendChild(loadingEl);
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loadingEl = document.getElementById('loading-overlay');
    if (loadingEl) {
        loadingEl.remove();
    }
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 为搜索添加防抖
const debouncedLoadData = debounce(loadHolidayData, 300);