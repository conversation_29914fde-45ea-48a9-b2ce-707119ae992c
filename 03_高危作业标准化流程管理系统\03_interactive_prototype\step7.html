<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤7：作业总结 - WEB端</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        .step-container { display: grid; grid-template-columns: 1fr 350px; gap: 20px; margin-top: 20px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .summary-card { background: #f8f9fa; border-radius: 8px; padding: 20px; text-align: center; border-left: 4px solid #4CAF50; }
        .summary-value { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 8px; }
        .summary-label { font-size: 12px; color: #666; }
        .chart-container { background: white; border-radius: 8px; padding: 20px; margin: 15px 0; height: 300px; display: flex; align-items: center; justify-content: center; border: 1px solid #e0e0e0; }
        .timeline-item { display: flex; align-items: center; padding: 15px 0; border-bottom: 1px solid #f0f0f0; }
        .timeline-item:last-child { border-bottom: none; }
        .timeline-dot { width: 12px; height: 12px; border-radius: 50%; background: #4CAF50; margin-right: 15px; }
        .timeline-content { flex: 1; }
        .timeline-title { font-weight: 500; color: #333; margin-bottom: 4px; }
        .timeline-time { font-size: 12px; color: #666; }
        .success-banner { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; border-radius: 12px; padding: 30px; text-align: center; margin-bottom: 20px; }
        .success-icon { font-size: 48px; margin-bottom: 15px; }
        .success-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .success-desc { font-size: 16px; opacity: 0.9; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div>
                    <div class="header-title">步骤7：作业总结</div>
                    <div class="header-subtitle">WEB端 - 数据汇总与效果评估</div>
                </div>
                <div class="header-user">
                    <div class="user-info">
                        <div class="user-name">张三</div>
                        <div class="user-role">作业负责人</div>
                    </div>
                    <div class="user-avatar">张</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 工作流程导航 -->
        <div class="workflow-container">
            <div class="workflow-steps" id="workflowSteps"></div>
        </div>

        <!-- 成功完成横幅 -->
        <div class="success-banner">
            <div class="success-icon">🎉</div>
            <div class="success-title">环流熏蒸作业圆满完成！</div>
            <div class="success-desc">作业编号：FUM-2024-001 | 总耗时：7天15小时30分钟</div>
        </div>

        <div class="step-container">
            <!-- 主要内容区域 -->
            <div>
                <!-- 关键指标汇总 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">关键指标汇总</div>
                        <div class="card-subtitle">本次熏蒸作业的核心数据统计</div>
                    </div>
                    <div class="card-body">
                        <div class="summary-grid">
                            <div class="summary-card">
                                <div class="summary-value">98.5%</div>
                                <div class="summary-label">杀虫效果</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-value">15.5kg</div>
                                <div class="summary-label">实际用药量</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-value">168小时</div>
                                <div class="summary-label">熏蒸周期</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-value">0.05mg/kg</div>
                                <div class="summary-label">药剂残留</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-value">500吨</div>
                                <div class="summary-label">处理粮食</div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-value">优秀</div>
                                <div class="summary-label">综合评价</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 效果分析图表 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">效果分析图表</div>
                        <div class="card-subtitle">PH3浓度变化趋势</div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <div style="text-align: center; color: #666;">
                                <div style="font-size: 48px; margin-bottom: 15px;">📊</div>
                                <div>PH3浓度变化曲线图</div>
                                <div style="font-size: 12px; margin-top: 8px;">（实际项目中将显示真实图表）</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 经验总结 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">经验总结</div>
                        <div class="card-subtitle">本次作业的经验与改进建议</div>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label class="form-label">成功经验</label>
                            <textarea class="form-control" rows="3" readonly>1. 严格按照标准操作流程执行，确保了作业安全
2. 实时监测系统发挥重要作用，及时发现并处理异常情况
3. 多部门协同配合良好，提高了作业效率</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">改进建议</label>
                            <textarea class="form-control" rows="3" readonly>1. 建议增加自动化监测设备，减少人工干预
2. 优化人员配置，提高专业技能培训
3. 完善应急预案，加强风险防控能力</textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">综合评价</label>
                            <select class="form-control" disabled>
                                <option selected>优秀 - 完全达到预期目标</option>
                            </select>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-secondary" onclick="exportReport()">导出报告</button>
                        <button class="btn btn-primary" onclick="archiveWork()">归档作业</button>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div>
                <!-- 作业时间线 -->
                <div class="card">
                    <div class="card-title">作业时间线</div>
                    <div style="margin-top: 15px;">
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">熏蒸方案制定</div>
                                <div class="timeline-time">2024-01-15 09:30 - 17:00</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">作业前准备</div>
                                <div class="timeline-time">2024-01-16 08:00 - 10:00</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">施药操作</div>
                                <div class="timeline-time">2024-01-16 10:30 - 12:00</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">补药操作</div>
                                <div class="timeline-time">2024-01-18 14:00 - 15:00</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">散气操作</div>
                                <div class="timeline-time">2024-01-23 08:00 - 16:00</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">效果检查</div>
                                <div class="timeline-time">2024-01-24 09:00 - 17:00</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-dot"></div>
                            <div class="timeline-content">
                                <div class="timeline-title">作业总结</div>
                                <div class="timeline-time">2024-01-25 09:00 - 完成</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 参与人员 -->
                <div class="card">
                    <div class="card-title">参与人员</div>
                    <div style="margin-top: 15px;">
                        <div style="display: flex; align-items: center; padding: 8px 0;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #4CAF50; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 12px; font-size: 12px;">张</div>
                            <div>
                                <div style="font-weight: 500; font-size: 14px;">张三</div>
                                <div style="font-size: 12px; color: #666;">作业负责人</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; padding: 8px 0;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #2196F3; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 12px; font-size: 12px;">李</div>
                            <div>
                                <div style="font-weight: 500; font-size: 14px;">李四</div>
                                <div style="font-size: 12px; color: #666;">安全员</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; padding: 8px 0;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #ff9800; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 12px; font-size: 12px;">王</div>
                            <div>
                                <div style="font-weight: 500; font-size: 14px;">王五</div>
                                <div style="font-size: 12px; color: #666;">操作人员</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; padding: 8px 0;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #9c27b0; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 12px; font-size: 12px;">赵</div>
                            <div>
                                <div style="font-weight: 500; font-size: 14px;">赵六</div>
                                <div style="font-size: 12px; color: #666;">检验员</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-title">快捷操作</div>
                    <div style="margin-top: 15px;">
                        <button class="btn btn-outline" style="width: 100%; margin-bottom: 10px;" onclick="viewDetails()">查看详细数据</button>
                        <button class="btn btn-outline" style="width: 100%; margin-bottom: 10px;" onclick="compareHistory()">历史对比分析</button>
                        <button class="btn btn-outline" style="width: 100%; margin-bottom: 10px;" onclick="shareReport()">分享报告</button>
                        <button class="btn btn-outline" style="width: 100%;" onclick="newWork()">创建新作业</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/common.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeWorkflowSteps();
            // 标记所有步骤为完成状态
            WORKFLOW_STEPS.forEach(step => {
                step.status = 'completed';
            });
            workflowManager.setCurrentStep(7);
        });

        // 初始化工作流程步骤
        function initializeWorkflowSteps() {
            const stepsContainer = document.getElementById('workflowSteps');
            stepsContainer.innerHTML = '';

            WORKFLOW_STEPS.forEach(step => {
                const stepElement = document.createElement('div');
                stepElement.className = 'workflow-step completed';
                stepElement.onclick = () => navigateToStep(step.id);
                
                stepElement.innerHTML = `
                    <div class="step-circle">✓</div>
                    <div class="step-title">${step.name}</div>
                    <div class="step-time">已完成</div>
                `;
                
                stepsContainer.appendChild(stepElement);
            });
        }

        function navigateToStep(stepId) {
            window.location.href = `step${stepId}.html`;
        }

        // 导出报告
        function exportReport() {
            NotificationManager.success('作业报告已导出到本地');
        }

        // 归档作业
        function archiveWork() {
            if (confirm('确认归档本次作业？归档后将无法修改。')) {
                workflowManager.completeStep(7, { 
                    status: 'archived',
                    archivedAt: new Date().toISOString()
                });
                
                NotificationManager.success('作业已成功归档！');
                
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 2000);
            }
        }

        // 查看详细数据
        function viewDetails() {
            NotificationManager.info('详细数据查看功能开发中...');
        }

        // 历史对比分析
        function compareHistory() {
            NotificationManager.info('历史对比分析功能开发中...');
        }

        // 分享报告
        function shareReport() {
            NotificationManager.info('报告分享功能开发中...');
        }

        // 创建新作业
        function newWork() {
            if (confirm('确认创建新的熏蒸作业？')) {
                window.location.href = 'index.html';
            }
        }

        // 返回按钮
        const backButton = document.createElement('button');
        backButton.className = 'btn btn-secondary';
        backButton.textContent = '← 返回主页';
        backButton.onclick = () => window.location.href = 'index.html';
        backButton.style.position = 'fixed';
        backButton.style.bottom = '20px';
        backButton.style.left = '20px';
        backButton.style.zIndex = '1000';
        document.body.appendChild(backButton);
    </script>
</body>
</html>
