<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中科抗菌 - 产品验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 0;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 20px 15px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            position: relative;
            z-index: 1;
        }
        
        .subtitle {
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .verification-section {
            padding: 20px 15px;
        }
        
        .status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            text-align: center;
            border: 3px solid #28a745;
            position: relative;
            overflow: hidden;
        }
        
        .status-card.invalid {
            border-color: #dc3545;
        }
        
        .status-card.warning {
            border-color: #ffc107;
        }

        .status-card.expired {
            border-color: #fd7e14;
        }

        .status-card.damaged {
            border-color: #6c757d;
        }
        
        .status-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }
        
        .status-icon {
            font-size: 60px;
            margin-bottom: 15px;
            display: block;
        }
        
        .status-text {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #28a745;
        }
        
        .status-text.invalid {
            color: #dc3545;
        }
        
        .status-text.warning {
            color: #ffc107;
        }

        .status-text.expired {
            color: #fd7e14;
        }

        .status-text.damaged {
            color: #6c757d;
        }
        
        .status-desc {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.5;
        }
        
        .product-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .product-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .info-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .info-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 4px;
        }
        
        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .video-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }
        
        .video-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 200px;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
        }
        
        .video-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
        }
        
        .play-button {
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 24px;
            color: #007bff;
            cursor: pointer;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .play-button:hover {
            background: white;
            transform: scale(1.1);
        }
        
        .certificates-section {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .certificates-section h3 {
            color: #856404;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .certificate-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .certificate-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .certificate-icon {
            font-size: 20px;
            color: #ffc107;
        }
        
        .certificate-text {
            flex: 1;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .verification-details {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .verification-details h3 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(25,118,210,0.1);
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-size: 14px;
            color: #1976d2;
        }
        
        .detail-value {
            font-size: 14px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 20px 15px;
            text-align: center;
        }
        
        .footer-logo {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .footer-text {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            flex-direction: column;
            gap: 15px;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 15px;
            border-left: 4px solid #dc3545;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-outline {
            background: white;
            color: #007bff;
            border: 2px solid #007bff;
        }

        .btn-outline:hover {
            background: #007bff;
            color: white;
        }

        .help-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .help-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .help-list {
            list-style: none;
            padding: 0;
        }

        .help-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            color: #495057;
            font-size: 14px;
        }

        .help-list li:last-child {
            border-bottom: none;
        }

        .contact-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
        }

        .contact-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .contact-info p {
            color: #1976d2;
            margin: 5px 0;
            font-size: 14px;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .warning-box h4 {
            color: #856404;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .warning-box p {
            color: #856404;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️ 中科抗菌</div>
            <div class="subtitle">专业抗菌纺织品验证</div>
        </div>
        
        <div class="verification-section">
            <!-- 加载状态 -->
            <div id="loadingSection" class="loading">
                <div class="spinner"></div>
                <div>正在验证标签...</div>
            </div>
            
            <!-- 验证结果 -->
            <div id="resultSection" style="display: none;">
                <!-- 验证状态卡片 -->
                <div class="status-card" id="statusCard">
                    <span class="status-icon" id="statusIcon">✅</span>
                    <div class="status-text" id="statusText">正版产品</div>
                    <div class="status-desc" id="statusDesc">恭喜！这是中科抗菌正版认证产品</div>
                </div>
                
                <!-- 产品信息 -->
                <div class="product-info">
                    <h3>📦 产品信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">产品名称</div>
                            <div class="info-value">中科抗菌纺织品</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产品型号</div>
                            <div class="info-value">ZK-AB-2024</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">生产批次</div>
                            <div class="info-value">20240315001</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">抗菌等级</div>
                            <div class="info-value">AAA级</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">生产日期</div>
                            <div class="info-value">2024-03-15</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">有效期限</div>
                            <div class="info-value">2027-03-15</div>
                        </div>
                    </div>
                </div>
                
                <!-- 视频展示 -->
                <div class="video-section">
                    <h3>🎥 专利获奖展示</h3>
                    <div class="video-container" onclick="playVideo()">
                        <div class="video-placeholder">
                            <button class="play-button">▶</button>
                            <div>点击播放专利获奖视频</div>
                        </div>
                    </div>
                </div>
                
                <!-- 认证证书 -->
                <div class="certificates-section">
                    <h3>🏆 认证证书</h3>
                    <div class="certificate-list">
                        <div class="certificate-item">
                            <span class="certificate-icon">🥇</span>
                            <span class="certificate-text">国家发明专利证书 (ZL202410123456.7)</span>
                        </div>
                        <div class="certificate-item">
                            <span class="certificate-icon">🏅</span>
                            <span class="certificate-text">中国纺织工业联合会科技进步一等奖</span>
                        </div>
                        <div class="certificate-item">
                            <span class="certificate-icon">🎖️</span>
                            <span class="certificate-text">抗菌材料行业标准认证 (GB/T 20944.3)</span>
                        </div>
                        <div class="certificate-item">
                            <span class="certificate-icon">⭐</span>
                            <span class="certificate-text">ISO 20743抗菌性能检测报告</span>
                        </div>
                    </div>
                </div>
                
                <!-- 验证详情 -->
                <div class="verification-details">
                    <h3>🔍 验证详情</h3>
                    <div class="detail-item">
                        <span class="detail-label">标签ID</span>
                        <span class="detail-value">NFC-ZK-20240315-001</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">验证次数</span>
                        <span class="detail-value">第 3 次验证</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">首次验证</span>
                        <span class="detail-value">2024-03-20 14:30</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">验证时间</span>
                        <span class="detail-value">2024-03-25 10:15</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">验证地点</span>
                        <span class="detail-value">北京市朝阳区</span>
                    </div>
                </div>
            </div>
            
            <!-- 错误信息 -->
            <div id="errorSection" style="display: none;">
                <div class="error-message">
                    <strong>验证失败</strong><br>
                    无法连接到验证服务器，请检查网络连接后重试。
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-logo">中科抗菌科技有限公司</div>
            <div class="footer-text">专业抗菌材料研发与生产 | 客服热线：************</div>
        </div>
    </div>
    
    <script>
        // 模拟NFC标签ID获取
        function getNFCTagId() {
            // 实际应用中这里会从NFC读取或URL参数获取
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('tagId') || 'NFC-ZK-20240315-001';
        }
        
        // 验证NFC标签
        async function verifyNFCTag(tagId) {
            try {
                // 模拟API调用
                const response = await fetch('/api/nfc/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ tagId: tagId })
                });
                
                if (!response.ok) {
                    throw new Error('验证请求失败');
                }
                
                return await response.json();
            } catch (error) {
                console.error('验证失败:', error);
                throw error;
            }
        }
        
        // 显示验证结果
        function showVerificationResult(result) {
            const statusCard = document.getElementById('statusCard');
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const statusDesc = document.getElementById('statusDesc');
            const resultSection = document.getElementById('resultSection');

            // 清空之前的内容
            resultSection.innerHTML = '';

            if (result.isValid) {
                // 成功状态 - 显示完整的产品信息
                showSuccessResult(result);
            } else {
                // 失败状态 - 根据失败类型显示不同界面
                showFailureResult(result);
            }
        }

        // 显示成功验证结果
        function showSuccessResult(result) {
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <!-- 验证状态卡片 -->
                <div class="status-card">
                    <span class="status-icon">✅</span>
                    <div class="status-text">正版产品</div>
                    <div class="status-desc">恭喜！这是中科抗菌正版认证产品</div>
                </div>

                <!-- 产品信息 -->
                <div class="product-info">
                    <h3>📦 产品信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">产品名称</div>
                            <div class="info-value">中科抗菌纺织品</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产品型号</div>
                            <div class="info-value">ZK-AB-2024</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">生产批次</div>
                            <div class="info-value">20240315001</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">抗菌等级</div>
                            <div class="info-value">AAA级</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">生产日期</div>
                            <div class="info-value">2024-03-15</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">有效期限</div>
                            <div class="info-value">2027-03-15</div>
                        </div>
                    </div>
                </div>

                <!-- 视频展示 -->
                <div class="video-section">
                    <h3>🎥 专利获奖展示</h3>
                    <div class="video-container" onclick="playVideo()">
                        <div class="video-placeholder">
                            <button class="play-button">▶</button>
                            <div>点击播放专利获奖视频</div>
                        </div>
                    </div>
                </div>

                <!-- 认证证书 -->
                <div class="certificates-section">
                    <h3>🏆 认证证书</h3>
                    <div class="certificate-list">
                        <div class="certificate-item">
                            <span class="certificate-icon">🥇</span>
                            <span class="certificate-text">国家发明专利证书 (ZL202410123456.7)</span>
                        </div>
                        <div class="certificate-item">
                            <span class="certificate-icon">🏅</span>
                            <span class="certificate-text">中国纺织工业联合会科技进步一等奖</span>
                        </div>
                        <div class="certificate-item">
                            <span class="certificate-icon">🎖️</span>
                            <span class="certificate-text">抗菌材料行业标准认证 (GB/T 20944.3)</span>
                        </div>
                        <div class="certificate-item">
                            <span class="certificate-icon">⭐</span>
                            <span class="certificate-text">ISO 20743抗菌性能检测报告</span>
                        </div>
                    </div>
                </div>

                <!-- 验证详情 -->
                <div class="verification-details">
                    <h3>🔍 验证详情</h3>
                    <div class="detail-item">
                        <span class="detail-label">标签ID</span>
                        <span class="detail-value">NFC-ZK-20240315-001</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">验证次数</span>
                        <span class="detail-value">第 3 次验证</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">首次验证</span>
                        <span class="detail-value">2024-03-20 14:30</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">验证时间</span>
                        <span class="detail-value">2024-03-25 10:15</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">验证地点</span>
                        <span class="detail-value">北京市朝阳区</span>
                    </div>
                </div>
            `;
        }

        // 显示失败验证结果
        function showFailureResult(result) {
            const resultSection = document.getElementById('resultSection');

            switch(result.errorType) {
                case 'fake':
                    showFakeProductResult(result);
                    break;
                case 'expired':
                    showExpiredResult(result);
                    break;
                case 'damaged':
                    showDamagedResult(result);
                    break;
                case 'network':
                    showNetworkErrorResult(result);
                    break;
                default:
                    showUnknownErrorResult(result);
            }
        }

        // 显示假冒产品结果
        function showFakeProductResult(result) {
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <!-- 警告状态卡片 -->
                <div class="status-card invalid">
                    <span class="status-icon">⚠️</span>
                    <div class="status-text invalid">警告！疑似假冒产品</div>
                    <div class="status-desc">此标签未通过官方验证，可能为伪造产品，请谨慎使用</div>
                </div>

                <!-- 风险提示 -->
                <div class="warning-box">
                    <h4>🚨 安全风险提示</h4>
                    <p>假冒产品可能存在以下风险：</p>
                    <ul style="margin: 10px 0; padding-left: 20px; color: #856404;">
                        <li>无抗菌功效，影响健康安全</li>
                        <li>材料质量不达标，可能含有害物质</li>
                        <li>无质量保证，使用寿命短</li>
                        <li>不享受官方售后服务</li>
                    </ul>
                </div>

                <!-- 如何识别正品 -->
                <div class="help-section">
                    <h3>🔍 如何识别正品</h3>
                    <ul class="help-list">
                        <li>✅ 正品标签具有独特的防伪特征</li>
                        <li>✅ 通过官方渠道购买</li>
                        <li>✅ 查看产品包装上的防伪码</li>
                        <li>✅ 验证专利证书编号</li>
                        <li>✅ 关注官方授权经销商标识</li>
                    </ul>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-danger" onclick="reportFakeProduct()">
                        🚨 举报假冒产品
                    </button>
                    <button class="btn btn-primary" onclick="retryVerification()">
                        🔄 重新验证
                    </button>
                    <button class="btn btn-outline" onclick="contactSupport()">
                        📞 联系客服
                    </button>
                </div>

                <!-- 联系信息 -->
                <div class="contact-info">
                    <h4>📞 官方客服</h4>
                    <p>客服热线：************</p>
                    <p>工作时间：9:00-18:00</p>
                    <p>微信客服：zkab_service</p>
                </div>
            `;
        }

        // 显示过期标签结果
        function showExpiredResult(result) {
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <!-- 过期状态卡片 -->
                <div class="status-card expired">
                    <span class="status-icon">⏰</span>
                    <div class="status-text expired">产品标签已过期</div>
                    <div class="status-desc">此产品标签已超过有效期，建议联系厂家更新</div>
                </div>

                <!-- 过期信息 -->
                <div class="product-info">
                    <h3>📅 过期信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">标签ID</div>
                            <div class="info-value">${result.tagId || 'NFC-ZK-20240315-002'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">过期时间</div>
                            <div class="info-value">2024-01-15</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">过期天数</div>
                            <div class="info-value">69天</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产品状态</div>
                            <div class="info-value">需要更新</div>
                        </div>
                    </div>
                </div>

                <!-- 处理建议 -->
                <div class="help-section">
                    <h3>💡 处理建议</h3>
                    <ul class="help-list">
                        <li>联系厂家客服申请标签更新</li>
                        <li>提供产品购买凭证</li>
                        <li>等待厂家邮寄新的NFC标签</li>
                        <li>或到授权服务点现场更新</li>
                    </ul>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-warning" onclick="requestUpdate()">
                        🔄 申请标签更新
                    </button>
                    <button class="btn btn-primary" onclick="retryVerification()">
                        🔄 重新验证
                    </button>
                    <button class="btn btn-outline" onclick="contactSupport()">
                        📞 联系客服
                    </button>
                </div>

                <!-- 联系信息 -->
                <div class="contact-info">
                    <h4>📞 标签更新服务</h4>
                    <p>客服热线：************</p>
                    <p>在线客服：zkab.com/service</p>
                    <p>邮箱：<EMAIL></p>
                </div>
            `;
        }

        // 显示标签损坏结果
        function showDamagedResult(result) {
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <!-- 损坏状态卡片 -->
                <div class="status-card damaged">
                    <span class="status-icon">📱</span>
                    <div class="status-text damaged">标签读取异常</div>
                    <div class="status-desc">NFC标签可能损坏或信号干扰，请尝试以下解决方法</div>
                </div>

                <!-- 故障排除 -->
                <div class="help-section">
                    <h3>🔧 故障排除步骤</h3>
                    <ul class="help-list">
                        <li>1. 确保手机NFC功能已开启</li>
                        <li>2. 将手机背面贴近标签位置</li>
                        <li>3. 保持手机稳定2-3秒</li>
                        <li>4. 避免金属物品干扰</li>
                        <li>5. 清洁标签表面，去除污渍</li>
                        <li>6. 尝试不同角度和位置</li>
                    </ul>
                </div>

                <!-- 可能原因 -->
                <div class="warning-box">
                    <h4>⚠️ 可能的原因</h4>
                    <p>• 标签物理损坏（弯折、刮擦）<br>
                    • 环境干扰（金属表面、电磁干扰）<br>
                    • 手机NFC功能异常<br>
                    • 标签老化或受潮</p>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="retryVerification()">
                        🔄 重新尝试读取
                    </button>
                    <button class="btn btn-secondary" onclick="checkNFCSettings()">
                        ⚙️ 检查NFC设置
                    </button>
                    <button class="btn btn-outline" onclick="contactSupport()">
                        📞 技术支持
                    </button>
                </div>

                <!-- 联系信息 -->
                <div class="contact-info">
                    <h4>🛠️ 技术支持</h4>
                    <p>技术热线：************</p>
                    <p>在线诊断：zkab.com/diagnosis</p>
                    <p>远程协助：QQ 800123456</p>
                </div>
            `;
        }

        // 显示网络错误结果
        function showNetworkErrorResult(result) {
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <!-- 网络错误状态卡片 -->
                <div class="status-card warning">
                    <span class="status-icon">📶</span>
                    <div class="status-text warning">验证服务暂时不可用</div>
                    <div class="status-desc">网络连接异常或服务器维护中，请稍后重试</div>
                </div>

                <!-- 网络检查 -->
                <div class="help-section">
                    <h3>🌐 网络检查</h3>
                    <ul class="help-list">
                        <li>检查手机网络连接状态</li>
                        <li>尝试切换WiFi或移动数据</li>
                        <li>确认网络信号强度</li>
                        <li>关闭VPN或代理服务</li>
                        <li>重启网络连接</li>
                    </ul>
                </div>

                <!-- 离线验证提示 -->
                <div class="warning-box">
                    <h4>💡 离线验证提示</h4>
                    <p>您可以通过以下方式进行基础验证：<br>
                    • 检查产品包装上的防伪标识<br>
                    • 查看专利证书编号<br>
                    • 对比官网产品图片<br>
                    • 联系官方客服人工验证</p>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="retryVerification()">
                        🔄 重新连接验证
                    </button>
                    <button class="btn btn-warning" onclick="offlineVerification()">
                        📋 离线验证指南
                    </button>
                    <button class="btn btn-outline" onclick="contactSupport()">
                        📞 人工验证
                    </button>
                </div>

                <!-- 联系信息 -->
                <div class="contact-info">
                    <h4>📞 人工验证服务</h4>
                    <p>客服热线：************</p>
                    <p>微信客服：zkab_verify</p>
                    <p>24小时在线服务</p>
                </div>
            `;
        }

        // 显示未知错误结果
        function showUnknownErrorResult(result) {
            const resultSection = document.getElementById('resultSection');
            resultSection.innerHTML = `
                <!-- 未知错误状态卡片 -->
                <div class="status-card warning">
                    <span class="status-icon">❓</span>
                    <div class="status-text warning">验证异常</div>
                    <div class="status-desc">验证过程中出现未知错误，请联系技术支持</div>
                </div>

                <!-- 错误信息 -->
                <div class="product-info">
                    <h3>🔍 错误详情</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">错误代码</div>
                            <div class="info-value">${result.errorCode || 'UNKNOWN_ERROR'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">错误时间</div>
                            <div class="info-value">${new Date().toLocaleString()}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">标签ID</div>
                            <div class="info-value">${result.tagId || '读取失败'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">设备信息</div>
                            <div class="info-value">${navigator.userAgent.includes('iPhone') ? 'iPhone' : 'Android'}</div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="retryVerification()">
                        🔄 重新验证
                    </button>
                    <button class="btn btn-danger" onclick="reportError()">
                        🐛 报告错误
                    </button>
                    <button class="btn btn-outline" onclick="contactSupport()">
                        📞 技术支持
                    </button>
                </div>

                <!-- 联系信息 -->
                <div class="contact-info">
                    <h4>🛠️ 技术支持</h4>
                    <p>技术热线：************</p>
                    <p>邮箱：<EMAIL></p>
                    <p>请提供错误代码以便快速定位问题</p>
                </div>
            `;
        }
        
        // 播放视频
        function playVideo() {
            // 这里可以打开视频播放器或跳转到视频页面
            alert('即将播放中科抗菌专利获奖展示视频');
            // 实际应用中可以这样实现：
            // window.open('video.html?id=zkab_awards', '_blank');
        }
        
        // 页面加载时执行验证
        window.addEventListener('load', async function() {
            const tagId = getNFCTagId();
            
            try {
                // 显示加载状态
                document.getElementById('loadingSection').style.display = 'flex';
                document.getElementById('resultSection').style.display = 'none';
                document.getElementById('errorSection').style.display = 'none';
                
                // 模拟验证延迟
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 执行验证（这里使用模拟数据）
                // 可以通过URL参数模拟不同的验证结果
                const urlParams = new URLSearchParams(window.location.search);
                const testType = urlParams.get('test') || 'success';

                let result;
                switch(testType) {
                    case 'fake':
                        result = {
                            isValid: false,
                            errorType: 'fake',
                            tagId: 'NFC-FAKE-20240315-999',
                            message: '检测到假冒产品'
                        };
                        break;
                    case 'expired':
                        result = {
                            isValid: false,
                            errorType: 'expired',
                            tagId: 'NFC-ZK-20240115-001',
                            expiredDate: '2024-01-15',
                            message: '产品标签已过期'
                        };
                        break;
                    case 'damaged':
                        result = {
                            isValid: false,
                            errorType: 'damaged',
                            tagId: 'NFC-ZK-20240315-001',
                            message: '标签读取异常'
                        };
                        break;
                    case 'network':
                        result = {
                            isValid: false,
                            errorType: 'network',
                            message: '网络连接失败'
                        };
                        break;
                    case 'unknown':
                        result = {
                            isValid: false,
                            errorType: 'unknown',
                            errorCode: 'ERR_UNKNOWN_001',
                            message: '未知验证错误'
                        };
                        break;
                    default:
                        result = {
                            isValid: true,
                            productInfo: {
                                name: '中科抗菌纺织品',
                                model: 'ZK-AB-2024',
                                batch: '20240315001',
                                level: 'AAA级'
                            }
                        };
                }
                
                // 显示结果
                showVerificationResult(result);
                document.getElementById('loadingSection').style.display = 'none';
                document.getElementById('resultSection').style.display = 'block';
                
            } catch (error) {
                // 显示错误
                document.getElementById('loadingSection').style.display = 'none';
                document.getElementById('errorSection').style.display = 'block';
            }
        });

        // 重新验证
        function retryVerification() {
            location.reload();
        }

        // 举报假冒产品
        function reportFakeProduct() {
            alert('感谢您的举报！我们将认真调查此假冒产品。\n\n请保留产品照片和购买凭证，客服人员将与您联系。');
            // 实际应用中这里会打开举报表单或跳转到举报页面
        }

        // 申请标签更新
        function requestUpdate() {
            alert('标签更新申请已提交！\n\n客服人员将在24小时内与您联系，请准备好产品购买凭证。');
            // 实际应用中这里会打开更新申请表单
        }

        // 检查NFC设置
        function checkNFCSettings() {
            alert('请按以下步骤检查NFC设置：\n\n1. 打开手机"设置"\n2. 找到"NFC"或"连接与共享"\n3. 确保NFC功能已开启\n4. 重新尝试验证');
        }

        // 离线验证
        function offlineVerification() {
            alert('离线验证指南：\n\n1. 检查产品包装防伪标识\n2. 查看专利证书编号\n3. 对比官网产品图片\n4. 联系客服人工验证\n\n客服热线：************');
        }

        // 联系客服
        function contactSupport() {
            if (confirm('是否要拨打客服热线？\n\n客服热线：************\n工作时间：9:00-18:00')) {
                // 在实际应用中，这里可以直接拨打电话
                window.location.href = 'tel:************';
            }
        }

        // 报告错误
        function reportError() {
            alert('错误报告已提交！\n\n技术人员将尽快处理此问题。\n如需紧急处理，请拨打技术热线：************');
            // 实际应用中这里会收集错误信息并发送到服务器
        }
    </script>
</body>
</html>
