# 环流熏蒸作业流程 - WEB端与APP端职责分工

## 一、总体分工原则

### WEB端职责
- **管理决策类功能**：方案制定、审批管理、数据分析、报告生成
- **监控展示类功能**：实时监控大屏、数据可视化、统计分析
- **配置管理类功能**：系统配置、用户管理、权限设置
- **文档处理类功能**：报告编制、文档管理、历史查询

### APP端职责  
- **现场操作类功能**：数据采集、现场记录、设备操作
- **移动办公类功能**：审批处理、消息通知、状态查看
- **实时交互类功能**：拍照上传、语音记录、位置定位
- **应急处理类功能**：异常报告、紧急联系、快速响应

## 二、环流熏蒸作业7步骤详细分工

### 步骤1：熏蒸方案制定 【WEB端主导】

#### WEB端功能
- **方案模板管理**
  - 预设方案模板库
  - 方案参数配置
  - 历史方案查询
  
- **智能方案生成**
  - 基于粮食品种自动推荐
  - 根据虫害情况计算用药量
  - 环境参数自动适配
  
- **方案审批流程**
  - 多级审批工作流
  - 审批意见记录
  - 审批状态跟踪
  
- **技术文档管理**
  - 方案文档生成
  - 技术标准查询
  - 法规合规检查

#### APP端协同功能
- **现场勘查记录**
  - 仓房现状拍照
  - 环境条件记录
  - 设备状态检查
  
- **移动审批**
  - 审批消息推送
  - 移动端审批操作
  - 审批结果通知

---

### 步骤2：作业前准备 【APP端主导】

#### APP端功能
- **检查清单执行**
  - 标准检查清单
  - 逐项检查确认
  - 异常情况记录
  
- **现场拍照记录**
  - 仓房密闭性检查
  - 设备安装情况
  - 安全防护措施
  
- **人员确认管理**
  - 作业人员签到
  - 资质证书验证
  - 安全交底确认
  
- **设备状态检查**
  - 设备运行测试
  - 参数校准确认
  - 故障排除记录

#### WEB端协同功能
- **准备状态监控**
  - 准备进度实时显示
  - 检查结果汇总
  - 异常情况预警
  
- **资源调度管理**
  - 人员调度安排
  - 设备资源分配
  - 物料需求确认

---

### 步骤3：施药操作 【APP端主导】

#### APP端功能
- **实时数据录入**
  - 施药时间记录
  - 用药量实时录入
  - 施药位置标记
  
- **现场操作记录**
  - 操作过程拍照
  - 操作人员确认
  - 异常情况报告
  
- **安全监测功能**
  - 个人防护检查
  - 环境安全监测
  - 紧急情况处理
  
- **设备操作界面**
  - 设备参数设置
  - 运行状态监控
  - 故障报警处理

#### WEB端协同功能
- **实时监控大屏**
  - 施药进度监控
  - 关键参数显示
  - 异常情况预警
  
- **数据分析处理**
  - 用药量统计分析
  - 效果预测模型
  - 历史数据对比

---

### 步骤4：补药操作 【APP端主导】

#### APP端功能
- **补药申请提交**
  - 补药原因说明
  - 补药量计算
  - 申请流程启动
  
- **现场补药记录**
  - 补药时间记录
  - 补药位置标记
  - 补药量确认
  
- **效果评估记录**
  - 补药前后对比
  - 浓度变化记录
  - 效果评估拍照

#### WEB端协同功能
- **补药申请审批**
  - 补药申请审核
  - 技术方案评估
  - 审批结果通知
  
- **补药效果分析**
  - 补药效果统计
  - 成本效益分析
  - 经验总结归档

---

### 步骤5：散气操作 【APP端主导】

#### APP端功能
- **散气时机判断**
  - 熏蒸时间确认
  - 浓度检测记录
  - 散气条件评估
  
- **散气过程记录**
  - 散气开始时间
  - 通风设备操作
  - 浓度变化监测
  
- **安全防护管理**
  - 人员防护检查
  - 安全区域设置
  - 应急预案准备

#### WEB端协同功能
- **散气监控管理**
  - 散气进度监控
  - 浓度变化曲线
  - 安全状态评估
  
- **散气效果分析**
  - 散气效率统计
  - 安全指标分析
  - 环境影响评估

---

### 步骤6：效果检查 【WEB端+APP端协同】

#### APP端功能
- **现场采样记录**
  - 采样点位标记
  - 采样过程拍照
  - 样品信息记录
  
- **初步检查记录**
  - 现场观察记录
  - 初步效果评估
  - 异常情况报告

#### WEB端功能
- **检验数据管理**
  - 实验室数据录入
  - 检验报告生成
  - 数据质量控制
  
- **效果分析评估**
  - 杀虫效果统计
  - 效果达标判断
  - 改进建议生成

---

### 步骤7：作业总结 【WEB端主导】

#### WEB端功能
- **数据汇总分析**
  - 全流程数据整合
  - 关键指标统计
  - 趋势分析报告
  
- **总结报告生成**
  - 自动报告生成
  - 图表可视化
  - 经验总结提炼
  
- **档案管理**
  - 作业档案归档
  - 历史数据存储
  - 查询检索功能

#### APP端协同功能
- **移动端查看**
  - 总结报告查看
  - 关键数据展示
  - 经验分享功能

## 三、协同工作机制

### 数据同步机制
- **实时同步**：关键操作数据实时同步
- **定时同步**：非关键数据定时批量同步
- **离线同步**：支持离线操作，网络恢复后自动同步
- **冲突处理**：数据冲突时的处理策略

### 消息通知机制
- **状态变更通知**：步骤状态变更时自动通知相关人员
- **异常报警通知**：异常情况发生时立即推送报警
- **审批流程通知**：审批节点到达时通知审批人
- **定时提醒通知**：重要时间节点的提醒通知

### 权限协同机制
- **角色权限同步**：WEB端和APP端权限保持一致
- **操作权限控制**：不同步骤的操作权限动态控制
- **数据访问权限**：基于角色的数据访问权限控制
- **审批权限流转**：审批权限在不同角色间的流转

## 四、技术实现要点

### 数据交互接口
- **RESTful API**：标准化的数据交互接口
- **WebSocket**：实时数据推送通道
- **文件上传接口**：图片、文档上传处理
- **离线数据接口**：离线数据存储和同步

### 用户体验设计
- **界面一致性**：WEB端和APP端保持视觉一致
- **操作连贯性**：跨端操作的流畅体验
- **数据一致性**：确保数据在不同端的一致性
- **响应及时性**：操作响应和状态更新的及时性

### 安全保障机制
- **身份认证**：统一的身份认证机制
- **数据加密**：敏感数据传输和存储加密
- **操作审计**：完整的操作日志记录
- **权限控制**：细粒度的权限控制机制

## 五、实施建议

### 开发优先级
1. **核心流程优先**：先实现主要业务流程
2. **WEB端先行**：先完成WEB端核心功能
3. **APP端跟进**：基于WEB端接口开发APP端
4. **协同功能完善**：最后完善协同工作功能

### 测试策略
- **单端测试**：分别测试WEB端和APP端功能
- **协同测试**：测试跨端协同工作功能
- **压力测试**：测试高并发情况下的系统性能
- **用户测试**：邀请实际用户进行使用测试

### 部署方案
- **分阶段部署**：按功能模块分阶段部署
- **灰度发布**：小范围试用后逐步推广
- **监控预警**：完善的系统监控和预警机制
- **应急预案**：制定系统故障应急处理预案
