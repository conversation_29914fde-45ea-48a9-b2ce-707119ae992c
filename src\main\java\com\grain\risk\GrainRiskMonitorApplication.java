package com.grain.risk;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 粮库风险监测系统启动类
 */
@SpringBootApplication
public class GrainRiskMonitorApplication {

    public static void main(String[] args) {
        SpringApplication.run(GrainRiskMonitorApplication.class, args);
        System.out.println("=================================");
        System.out.println("粮库风险监测系统启动成功！");
        System.out.println("访问地址：http://localhost:8080/grain-risk/holiday-management.html");
        System.out.println("=================================");
    }
}
