<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速配置向导 - 步骤2：差异化配置</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            background: rgba(255,255,255,0.2);
            height: 8px;
            border-radius: 4px;
            margin-top: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: white;
            height: 100%;
            width: 66.66%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 20px;
        }
        
        .step {
            display: flex;
            align-items: center;
            color: rgba(255,255,255,0.7);
            font-size: 14px;
        }
        
        .step.active {
            color: white;
            font-weight: bold;
        }
        
        .step.completed {
            color: rgba(255,255,255,0.9);
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 12px;
        }
        
        .step.active .step-number {
            background: white;
            color: #ff9800;
        }
        
        .step.completed .step-number {
            background: rgba(255,255,255,0.9);
            color: #ff9800;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .config-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }
        
        .config-section h2 {
            color: #2c3e50;
            font-size: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .section-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .base-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .extension-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        
        .extension-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .extension-item:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .extension-item.selected {
            border-color: #28a745;
            background: #f8fff9;
            box-shadow: 0 4px 15px rgba(40,167,69,0.2);
        }
        
        .extension-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .extension-checkbox {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            cursor: pointer;
        }
        
        .extension-icon {
            font-size: 20px;
            margin-right: 8px;
        }
        
        .extension-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .extension-description {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.4;
            margin-left: 32px;
        }
        
        .new-badge {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 8px;
        }
        
        .preview-section {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .preview-section h3 {
            color: #856404;
            font-size: 18px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .preview-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
        }
        
        .preview-item h4 {
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .preview-item p {
            color: #6c757d;
            font-size: 13px;
        }
        
        .preview-changes {
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .preview-changes h4 {
            color: #155724;
            margin-bottom: 8px;
        }
        
        .preview-changes p {
            color: #155724;
            font-size: 14px;
        }
        
        .action-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border: none;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ 差异化配置</h1>
            <p>根据您的需求，选择需要的扩展功能</p>
            
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            
            <div class="step-indicator">
                <div class="step completed">
                    <span class="step-number">✓</span>
                    选择模板
                </div>
                <div class="step active">
                    <span class="step-number">2</span>
                    差异化配置
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    确认生效
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <!-- 流程节点配置 -->
            <div class="config-section">
                <h2>
                    <span class="section-icon">🔀</span>
                    流程节点配置
                </h2>

                <div class="base-info">
                    <strong>当前流程：</strong>5个节点（申请提交 → 部门审批 → 安全检查 → 开始作业 → 作业验收）
                </div>

                <div style="background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="margin-bottom: 15px; color: #2c3e50;">流程配置方式</h4>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="extension-item" onclick="selectConfigMode('simple')">
                            <div class="extension-header">
                                <input type="radio" name="config-mode" value="simple" checked>
                                <span class="extension-icon">☑️</span>
                                <span class="extension-title">快速配置模式</span>
                                <span class="new-badge">推荐</span>
                            </div>
                            <div class="extension-description">
                                通过勾选预设节点快速配置流程，简单易用
                            </div>
                        </div>

                        <div class="extension-item" onclick="selectConfigMode('advanced')">
                            <div class="extension-header">
                                <input type="radio" name="config-mode" value="advanced">
                                <span class="extension-icon">🎨</span>
                                <span class="extension-title">可视化设计模式</span>
                                <span class="new-badge">专业</span>
                            </div>
                            <div class="extension-description">
                                拖拽节点自由设计流程，支持节点增减和顺序调整
                            </div>
                        </div>
                    </div>
                </div>

                <div id="simpleMode">
                    <h4 style="margin-bottom: 15px; color: #2c3e50;">选择要添加的节点</h4>
                    <div class="extension-grid">
                        <div class="extension-item selected" onclick="toggleExtension(this)">
                            <div class="extension-header">
                                <input type="checkbox" class="extension-checkbox" checked>
                                <span class="extension-icon">🌱</span>
                                <span class="extension-title">环保审批节点</span>
                                <span class="new-badge">推荐</span>
                            </div>
                            <div class="extension-description">
                                在部门审批后增加环保部门审批环节
                            </div>
                        </div>

                        <div class="extension-item" onclick="toggleExtension(this)">
                            <div class="extension-header">
                                <input type="checkbox" class="extension-checkbox">
                                <span class="extension-icon">📢</span>
                                <span class="extension-title">周边通知节点</span>
                            </div>
                            <div class="extension-description">
                                在安全检查前通知周边单位
                            </div>
                        </div>

                        <div class="extension-item selected" onclick="toggleExtension(this)">
                            <div class="extension-header">
                                <input type="checkbox" class="extension-checkbox" checked>
                                <span class="extension-icon">📷</span>
                                <span class="extension-title">现场拍照节点</span>
                                <span class="new-badge">热门</span>
                            </div>
                            <div class="extension-description">
                                在开始作业前进行现场拍照记录
                            </div>
                        </div>

                        <div class="extension-item" onclick="toggleExtension(this)">
                            <div class="extension-header">
                                <input type="checkbox" class="extension-checkbox">
                                <span class="extension-icon">🧪</span>
                                <span class="extension-title">气体检测节点</span>
                            </div>
                            <div class="extension-description">
                                在开始作业前进行气体浓度检测
                            </div>
                        </div>

                        <div class="extension-item" onclick="toggleExtension(this)">
                            <div class="extension-header">
                                <input type="checkbox" class="extension-checkbox">
                                <span class="extension-icon">👁️</span>
                                <span class="extension-title">实时监控节点</span>
                            </div>
                            <div class="extension-description">
                                在作业过程中进行实时监控
                            </div>
                        </div>

                        <div class="extension-item" onclick="toggleExtension(this)">
                            <div class="extension-header">
                                <input type="checkbox" class="extension-checkbox">
                                <span class="extension-icon">📋</span>
                                <span class="extension-title">完工检查节点</span>
                            </div>
                            <div class="extension-description">
                                在作业验收前进行完工检查
                            </div>
                        </div>
                    </div>
                </div>

                <div id="advancedMode" style="display: none;">
                    <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; text-align: center;">
                        <h4 style="color: #1976d2; margin-bottom: 10px;">🎨 可视化流程设计器</h4>
                        <p style="color: #1976d2; margin-bottom: 15px;">拖拽节点自由设计流程，支持节点增减和顺序调整</p>
                        <button class="btn btn-primary" onclick="openFlowDesigner()">
                            🚀 打开流程设计器
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 表单字段扩展 -->
            <div class="config-section">
                <h2>
                    <span class="section-icon">📝</span>
                    表单字段扩展
                </h2>
                
                <div class="base-info">
                    <strong>基础表单：</strong>12个字段（作业标题、地点、时间、人员、风险评估、安全措施等）
                </div>
                
                <div class="extension-grid">
                    <div class="extension-item selected" onclick="toggleExtension(this)">
                        <div class="extension-header">
                            <input type="checkbox" class="extension-checkbox" checked>
                            <span class="extension-icon">📍</span>
                            <span class="extension-title">GPS定位字段</span>
                            <span class="new-badge">智能</span>
                        </div>
                        <div class="extension-description">
                            自动获取作业位置，精确记录作业地点
                        </div>
                    </div>
                    
                    <div class="extension-item selected" onclick="toggleExtension(this)">
                        <div class="extension-header">
                            <input type="checkbox" class="extension-checkbox" checked>
                            <span class="extension-icon">📱</span>
                            <span class="extension-title">监护人手机字段</span>
                        </div>
                        <div class="extension-description">
                            记录监护人联系方式，便于紧急情况联系
                        </div>
                    </div>
                    
                    <div class="extension-item" onclick="toggleExtension(this)">
                        <div class="extension-header">
                            <input type="checkbox" class="extension-checkbox">
                            <span class="extension-icon">🚨</span>
                            <span class="extension-title">应急联系人字段</span>
                        </div>
                        <div class="extension-description">
                            设置紧急情况联系人，确保应急响应及时
                        </div>
                    </div>
                    
                    <div class="extension-item selected" onclick="toggleExtension(this)">
                        <div class="extension-header">
                            <input type="checkbox" class="extension-checkbox" checked>
                            <span class="extension-icon">📸</span>
                            <span class="extension-title">现场照片字段</span>
                        </div>
                        <div class="extension-description">
                            上传现场环境照片，直观展示作业环境
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 规则参数调整 -->
            <div class="config-section">
                <h2>
                    <span class="section-icon">📏</span>
                    规则参数调整
                </h2>
                
                <div class="base-info">
                    <strong>基础规则：</strong>标准安全要求（作业时间8:00-18:00，审批时限2小时，最少2人等）
                </div>
                
                <div class="extension-grid">
                    <div class="extension-item" onclick="toggleExtension(this)">
                        <div class="extension-header">
                            <input type="checkbox" class="extension-checkbox">
                            <span class="extension-icon">⏰</span>
                            <span class="extension-title">调整作业时间窗口</span>
                        </div>
                        <div class="extension-description">
                            默认：8:00-18:00，可调整为其他时间段
                        </div>
                    </div>
                    
                    <div class="extension-item selected" onclick="toggleExtension(this)">
                        <div class="extension-header">
                            <input type="checkbox" class="extension-checkbox" checked>
                            <span class="extension-icon">⚡</span>
                            <span class="extension-title">调整审批时限</span>
                            <span class="new-badge">优化</span>
                        </div>
                        <div class="extension-description">
                            默认：2小时 → 调整为1小时，提高审批效率
                        </div>
                    </div>
                    
                    <div class="extension-item" onclick="toggleExtension(this)">
                        <div class="extension-header">
                            <input type="checkbox" class="extension-checkbox">
                            <span class="extension-icon">👥</span>
                            <span class="extension-title">调整人员数量要求</span>
                        </div>
                        <div class="extension-description">
                            默认：最少2人，可根据实际情况调整
                        </div>
                    </div>
                    
                    <div class="extension-item" onclick="toggleExtension(this)">
                        <div class="extension-header">
                            <input type="checkbox" class="extension-checkbox">
                            <span class="extension-icon">🧪</span>
                            <span class="extension-title">调整检测标准</span>
                        </div>
                        <div class="extension-description">
                            默认：可燃气体<25%LEL，可调整检测标准
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 配置预览 -->
            <div class="preview-section">
                <h3>
                    <span class="section-icon">📊</span>
                    配置后效果预览
                </h3>
                
                <div class="preview-grid">
                    <div class="preview-item">
                        <h4>流程节点</h4>
                        <p>7个（+2个）</p>
                    </div>
                    <div class="preview-item">
                        <h4>表单字段</h4>
                        <p>15个（+3个）</p>
                    </div>
                    <div class="preview-item">
                        <h4>预计审批时间</h4>
                        <p>1-2小时</p>
                    </div>
                    <div class="preview-item">
                        <h4>新增功能</h4>
                        <p>环保审批、现场拍照、GPS定位</p>
                    </div>
                </div>
                
                <div class="preview-changes">
                    <h4>✨ 主要变更内容</h4>
                    <p>• 增加环保审批和现场拍照节点，提升作业规范性<br>
                    • 增加GPS定位、监护人手机、现场照片字段，完善信息记录<br>
                    • 审批时限从2小时缩短到1小时，提高处理效率</p>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="快速配置向导.html" class="btn btn-secondary">
                    ← 上一步：重新选择模板
                </a>
                
                <div style="display: flex; gap: 15px;">
                    <button class="btn btn-warning" onclick="window.open('流程对比展示.html', '_blank')">
                        👀 预览配置效果
                    </button>
                    <a href="确认生效.html" class="btn btn-success">
                        下一步：确认生效 →
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleExtension(element) {
            const checkbox = element.querySelector('.extension-checkbox');
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                element.classList.add('selected');
            } else {
                element.classList.remove('selected');
            }
            
            updatePreview();
        }
        
        function updatePreview() {
            // 这里可以根据选择的扩展项更新预览内容
            const selectedExtensions = document.querySelectorAll('.extension-item.selected').length;
            console.log('已选择扩展项数量:', selectedExtensions);
        }

        function selectConfigMode(mode) {
            const simpleMode = document.getElementById('simpleMode');
            const advancedMode = document.getElementById('advancedMode');

            if (mode === 'simple') {
                simpleMode.style.display = 'block';
                advancedMode.style.display = 'none';
            } else {
                simpleMode.style.display = 'none';
                advancedMode.style.display = 'block';
            }
        }

        function openFlowDesigner() {
            // 打开流程设计器
            window.open('流程节点配置.html', '_blank');
        }
    </script>
</body>
</html>
