<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NFC标签详情 - 中科抗菌管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            color: white;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
        }
        
        .status-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .status-icon.valid {
            background: #28a745;
        }
        
        .status-icon.invalid {
            background: #dc3545;
        }
        
        .status-icon.pending {
            background: #ffc107;
            color: #212529;
        }
        
        .status-info h2 {
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .status-info p {
            color: #6c757d;
            font-size: 14px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .info-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .info-value {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .verification-log {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .log-item:last-child {
            border-bottom: none;
        }
        
        .log-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
        }
        
        .log-icon.success {
            background: #28a745;
        }
        
        .log-icon.danger {
            background: #dc3545;
        }
        
        .log-content {
            flex: 1;
        }
        
        .log-title {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 4px;
        }
        
        .log-desc {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 2px;
        }
        
        .log-location {
            font-size: 11px;
            color: #007bff;
        }
        
        .log-time {
            font-size: 12px;
            color: #6c757d;
            white-space: nowrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 1px solid #ddd;
        }
        
        .btn-outline:hover {
            background: #f8f9fa;
        }
        
        .qr-code {
            text-align: center;
            padding: 20px;
        }
        
        .qr-placeholder {
            width: 150px;
            height: 150px;
            background: #f8f9fa;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            color: #6c757d;
            font-size: 14px;
        }
        
        .stats-mini {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .stat-mini {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-mini-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-mini-label {
            font-size: 12px;
            color: #6c757d;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .alert-danger {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .alert-success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .back-button {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="breadcrumb">
                <a href="管理端主界面.html">🏠 首页</a>
                <span>></span>
                <a href="管理端主界面.html">NFC标签管理</a>
                <span>></span>
                <span>标签详情</span>
            </div>
            <div>
                <button class="btn btn-outline" onclick="window.close()">关闭</button>
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <div class="back-button">
            <button class="btn btn-outline" onclick="window.history.back()">
                ← 返回列表
            </button>
        </div>
        
        <div class="detail-grid">
            <!-- 左侧主要信息 -->
            <div>
                <!-- 基本信息 -->
                <div class="card">
                    <div class="status-header">
                        <div class="status-icon valid">✅</div>
                        <div class="status-info">
                            <h2>NFC-ZK-20240315-001</h2>
                            <p>标签状态：有效 | 创建时间：2024-03-15 14:30</p>
                        </div>
                    </div>
                    
                    <div class="alert alert-success">
                        <strong>✅ 验证通过</strong><br>
                        此标签已通过官方验证，为正版中科抗菌产品标识。
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">产品名称</div>
                            <div class="info-value">中科抗菌纺织品</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">产品型号</div>
                            <div class="info-value">ZK-AB-2024</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">生产批次</div>
                            <div class="info-value">20240315001</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">抗菌等级</div>
                            <div class="info-value">AAA级</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">生产日期</div>
                            <div class="info-value">2024-03-15</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">有效期限</div>
                            <div class="info-value">2027-03-15</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">专利证书</div>
                            <div class="info-value">ZL202410123456.7</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">检测报告</div>
                            <div class="info-value">ISO20743-2024-001</div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="editTag()">
                            ✏️ 编辑标签
                        </button>
                        <button class="btn btn-success" onclick="generateQR()">
                            📱 生成二维码
                        </button>
                        <button class="btn btn-outline" onclick="exportData()">
                            📤 导出数据
                        </button>
                        <button class="btn btn-danger" onclick="disableTag()">
                            🚫 禁用标签
                        </button>
                    </div>
                </div>
                
                <!-- 验证记录 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            📋 验证记录
                        </h3>
                        <div>
                            <select style="padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px;">
                                <option>最近30天</option>
                                <option>最近7天</option>
                                <option>全部记录</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="verification-log">
                        <div class="log-item">
                            <div class="log-icon success">✅</div>
                            <div class="log-content">
                                <div class="log-title">验证成功</div>
                                <div class="log-desc">用户通过手机NFC验证</div>
                                <div class="log-location">📍 北京市朝阳区建国路88号</div>
                            </div>
                            <div class="log-time">2024-03-25<br>10:15:32</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon success">✅</div>
                            <div class="log-content">
                                <div class="log-title">验证成功</div>
                                <div class="log-desc">用户通过手机NFC验证</div>
                                <div class="log-location">📍 北京市海淀区中关村大街1号</div>
                            </div>
                            <div class="log-time">2024-03-24<br>15:22:18</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon success">✅</div>
                            <div class="log-content">
                                <div class="log-title">验证成功</div>
                                <div class="log-desc">用户通过手机NFC验证</div>
                                <div class="log-location">📍 上海市浦东新区陆家嘴环路1000号</div>
                            </div>
                            <div class="log-time">2024-03-23<br>09:45:12</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon danger">❌</div>
                            <div class="log-content">
                                <div class="log-title">验证失败</div>
                                <div class="log-desc">标签读取异常，可能是信号干扰</div>
                                <div class="log-location">📍 广州市天河区珠江新城</div>
                            </div>
                            <div class="log-time">2024-03-22<br>16:30:45</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon success">✅</div>
                            <div class="log-content">
                                <div class="log-title">验证成功</div>
                                <div class="log-desc">用户通过手机NFC验证</div>
                                <div class="log-location">📍 深圳市南山区科技园</div>
                            </div>
                            <div class="log-time">2024-03-21<br>11:20:33</div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-icon success">✅</div>
                            <div class="log-content">
                                <div class="log-title">首次验证</div>
                                <div class="log-desc">标签激活，首次验证成功</div>
                                <div class="log-location">📍 北京市朝阳区工厂地址</div>
                            </div>
                            <div class="log-time">2024-03-20<br>14:30:00</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧统计信息 -->
            <div>
                <!-- 验证统计 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📊 验证统计</h3>
                    </div>
                    
                    <div class="stats-mini">
                        <div class="stat-mini">
                            <div class="stat-mini-value">23</div>
                            <div class="stat-mini-label">总验证次数</div>
                        </div>
                        <div class="stat-mini">
                            <div class="stat-mini-value">21</div>
                            <div class="stat-mini-label">成功次数</div>
                        </div>
                        <div class="stat-mini">
                            <div class="stat-mini-value">91.3%</div>
                            <div class="stat-mini-label">成功率</div>
                        </div>
                        <div class="stat-mini">
                            <div class="stat-mini-value">5</div>
                            <div class="stat-mini-label">本周验证</div>
                        </div>
                    </div>
                </div>
                
                <!-- 二维码 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">📱 验证二维码</h3>
                    </div>
                    
                    <div class="qr-code">
                        <div class="qr-placeholder">
                            📱<br>二维码<br>（点击生成）
                        </div>
                        <p style="font-size: 12px; color: #6c757d; margin-bottom: 15px;">
                            扫描二维码可直接验证标签
                        </p>
                        <button class="btn btn-primary" onclick="generateQR()">
                            生成二维码
                        </button>
                    </div>
                </div>
                
                <!-- 地理分布 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🗺️ 验证地区分布</h3>
                    </div>
                    
                    <div style="font-size: 14px; line-height: 1.6;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>北京市</span>
                            <span><strong>12次</strong></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>上海市</span>
                            <span><strong>5次</strong></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>广州市</span>
                            <span><strong>3次</strong></span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>深圳市</span>
                            <span><strong>2次</strong></span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span>其他地区</span>
                            <span><strong>1次</strong></span>
                        </div>
                    </div>
                </div>
                
                <!-- 风险提示 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">⚠️ 风险提示</h3>
                    </div>
                    
                    <div class="alert alert-warning">
                        <strong>注意</strong><br>
                        该标签在过去7天内有1次验证失败记录，建议关注标签状态。
                    </div>
                    
                    <div style="font-size: 14px; color: #6c757d;">
                        <p>• 标签状态正常</p>
                        <p>• 验证成功率在正常范围内</p>
                        <p>• 无异常验证行为</p>
                        <p>• 距离过期还有1095天</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 编辑标签
        function editTag() {
            window.open('标签编辑页面.html?id=NFC-ZK-20240315-001', '_blank');
        }
        
        // 生成二维码
        function generateQR() {
            alert('正在生成二维码...');
            // 实际应用中这里会调用二维码生成API
        }
        
        // 导出数据
        function exportData() {
            alert('正在导出标签数据...');
            // 实际应用中这里会调用导出API
        }
        
        // 禁用标签
        function disableTag() {
            if (confirm('确定要禁用此标签吗？禁用后将无法通过验证。')) {
                alert('标签已禁用');
                // 实际应用中这里会调用禁用API
            }
        }
        
        // 页面加载时获取标签详情
        window.addEventListener('load', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tagId = urlParams.get('id');
            
            if (tagId) {
                // 根据tagId加载具体的标签信息
                console.log('加载标签详情:', tagId);
            }
        });
    </script>
</body>
</html>
