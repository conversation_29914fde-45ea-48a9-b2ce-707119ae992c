# 高危作业标准化流程管理系统 - 详细设计文档

## 一、系统架构设计

### 1.1 总体架构
系统采用前后端分离的微服务架构，支持高并发、高可用、易扩展的设计原则。

```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   PC Web端      │   移动端App     │   管理后台              │
│   (Vue.js)      │   (React Native)│   (Vue.js + Element)    │
└─────────────────┴─────────────────┴─────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                    API网关层                                │
│              (Spring Cloud Gateway)                        │
└─────────────────────────────────────────────────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   微服务层                                  │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│  用户服务   │  流程服务   │  表单服务   │   监控服务      │
│ (User)      │ (Workflow)  │ (Form)      │  (Monitor)      │
├─────────────┼─────────────┼─────────────┼─────────────────┤
│  通知服务   │  文件服务   │  报表服务   │   设备服务      │
│ (Notice)    │ (File)      │ (Report)    │  (Device)       │
└─────────────┴─────────────┴─────────────┴─────────────────┘
                            │
┌─────────────────────────────────────────────────────────────┐
│                   数据层                                    │
├─────────────┬─────────────┬─────────────┬─────────────────┤
│   MySQL     │    Redis    │   MongoDB   │   Elasticsearch │
│  (主数据)   │   (缓存)    │  (文档)     │    (搜索)       │
└─────────────┴─────────────┴─────────────┴─────────────────┘
```

### 1.2 技术栈选择

#### 1.2.1 后端技术栈
- **框架**: Spring Boot 2.7.x + Spring Cloud 2021.x
- **数据库**: MySQL 8.0 (主数据库) + Redis 6.x (缓存)
- **消息队列**: RabbitMQ 3.9.x
- **搜索引擎**: Elasticsearch 7.x
- **文档存储**: MongoDB 5.0.x
- **API文档**: Swagger 3.x
- **监控**: Spring Boot Actuator + Micrometer

#### 1.2.2 前端技术栈
- **PC端**: Vue.js 3.x + Element Plus + TypeScript
- **移动端**: React Native 0.70.x + TypeScript
- **状态管理**: Vuex 4.x (PC端) / Redux Toolkit (移动端)
- **HTTP客户端**: Axios
- **构建工具**: Vite (PC端) / Metro (移动端)

### 1.3 部署架构
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (生产环境)
- **负载均衡**: Nginx + Keepalived
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack (Elasticsearch + Logstash + Kibana)

## 二、数据库设计

### 2.1 数据库选择
- **主数据库**: MySQL 8.0 - 存储结构化业务数据
- **缓存数据库**: Redis 6.x - 缓存热点数据，提升性能
- **文档数据库**: MongoDB 5.0 - 存储非结构化数据（文件、日志等）
- **搜索引擎**: Elasticsearch 7.x - 全文搜索和数据分析

### 2.2 核心数据表设计

#### 2.2.1 用户管理相关表

```sql
-- 用户表
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    dept_id BIGINT COMMENT '部门ID',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_dept_id (dept_id)
) COMMENT '用户表';

-- 角色表
CREATE TABLE sys_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    description VARCHAR(200) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态:1启用,0禁用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '角色表';

-- 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_role (user_id, role_id)
) COMMENT '用户角色关联表';
```

#### 2.2.2 作业流程相关表

```sql
-- 作业申请表
CREATE TABLE work_application (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    app_no VARCHAR(50) NOT NULL UNIQUE COMMENT '申请编号',
    work_type VARCHAR(20) NOT NULL COMMENT '作业类型',
    work_title VARCHAR(200) NOT NULL COMMENT '作业标题',
    work_content TEXT COMMENT '作业内容',
    applicant_id BIGINT NOT NULL COMMENT '申请人ID',
    dept_id BIGINT NOT NULL COMMENT '申请部门ID',
    planned_start_time DATETIME NOT NULL COMMENT '计划开始时间',
    planned_end_time DATETIME NOT NULL COMMENT '计划结束时间',
    work_location VARCHAR(200) COMMENT '作业地点',
    risk_level TINYINT DEFAULT 1 COMMENT '风险等级:1低,2中,3高',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_app_no (app_no),
    INDEX idx_work_type (work_type),
    INDEX idx_status (status),
    INDEX idx_applicant (applicant_id)
) COMMENT '作业申请表';

-- 作业审批记录表
CREATE TABLE work_approval (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    app_id BIGINT NOT NULL COMMENT '申请ID',
    approver_id BIGINT NOT NULL COMMENT '审批人ID',
    approval_level TINYINT NOT NULL COMMENT '审批级别',
    approval_result VARCHAR(20) NOT NULL COMMENT '审批结果',
    approval_opinion TEXT COMMENT '审批意见',
    approval_time DATETIME NOT NULL COMMENT '审批时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_app_id (app_id),
    INDEX idx_approver (approver_id)
) COMMENT '作业审批记录表';
```

#### 2.2.3 环流熏蒸专项表

```sql
-- 熏蒸作业表
CREATE TABLE fumigation_work (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    app_id BIGINT NOT NULL COMMENT '作业申请ID',
    warehouse_id BIGINT NOT NULL COMMENT '仓房ID',
    grain_type VARCHAR(50) NOT NULL COMMENT '粮食品种',
    grain_quantity DECIMAL(10,2) NOT NULL COMMENT '粮食数量(吨)',
    pest_type VARCHAR(100) COMMENT '害虫种类',
    pest_density DECIMAL(8,2) COMMENT '虫害密度',
    fumigant_type VARCHAR(50) NOT NULL COMMENT '熏蒸剂类型',
    fumigant_dosage DECIMAL(8,2) NOT NULL COMMENT '熏蒸剂用量(g/m³)',
    fumigation_period INT NOT NULL COMMENT '熏蒸周期(小时)',
    temperature_range VARCHAR(20) COMMENT '温度范围',
    humidity_range VARCHAR(20) COMMENT '湿度范围',
    status VARCHAR(20) DEFAULT 'PLANNED' COMMENT '状态',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_app_id (app_id),
    INDEX idx_warehouse (warehouse_id),
    INDEX idx_status (status)
) COMMENT '熏蒸作业表';

-- 熏蒸监测数据表
CREATE TABLE fumigation_monitor (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    fumigation_id BIGINT NOT NULL COMMENT '熏蒸作业ID',
    monitor_time DATETIME NOT NULL COMMENT '监测时间',
    temperature DECIMAL(5,2) COMMENT '温度(℃)',
    humidity DECIMAL(5,2) COMMENT '湿度(%)',
    ph3_concentration DECIMAL(8,2) COMMENT '磷化氢浓度(mg/m³)',
    o2_concentration DECIMAL(5,2) COMMENT '氧气浓度(%)',
    co2_concentration DECIMAL(8,2) COMMENT '二氧化碳浓度(ppm)',
    monitor_point VARCHAR(50) COMMENT '监测点位',
    operator_id BIGINT COMMENT '操作员ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_fumigation_id (fumigation_id),
    INDEX idx_monitor_time (monitor_time)
) COMMENT '熏蒸监测数据表';
```

### 2.3 数据库优化策略

#### 2.3.1 索引优化
- 为高频查询字段建立合适的索引
- 使用复合索引优化多条件查询
- 定期分析索引使用情况，清理无效索引

#### 2.3.2 分区策略
- 对历史数据表按时间进行分区
- 监测数据表按月分区，提升查询性能
- 日志表按日分区，便于数据清理

#### 2.3.3 读写分离
- 主库负责写操作和实时性要求高的读操作
- 从库负责报表查询和数据分析
- 使用中间件实现自动读写分离

## 三、接口设计

### 3.1 RESTful API设计规范

#### 3.1.1 URL设计规范
```
GET    /api/v1/users              # 获取用户列表
GET    /api/v1/users/{id}         # 获取指定用户
POST   /api/v1/users              # 创建用户
PUT    /api/v1/users/{id}         # 更新用户
DELETE /api/v1/users/{id}         # 删除用户

GET    /api/v1/work-applications  # 获取作业申请列表
POST   /api/v1/work-applications  # 创建作业申请
PUT    /api/v1/work-applications/{id}/approve  # 审批作业申请
```

#### 3.1.2 响应格式规范
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 3.2 核心接口定义

#### 3.2.1 用户认证接口
```yaml
/api/v1/auth/login:
  post:
    summary: 用户登录
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              username:
                type: string
                description: 用户名
              password:
                type: string
                description: 密码
              captcha:
                type: string
                description: 验证码
    responses:
      200:
        description: 登录成功
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: JWT令牌
                userInfo:
                  type: object
                  description: 用户信息
```

#### 3.2.2 作业申请接口
```yaml
/api/v1/work-applications:
  post:
    summary: 创建作业申请
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              workType:
                type: string
                description: 作业类型
              workTitle:
                type: string
                description: 作业标题
              workContent:
                type: string
                description: 作业内容
              plannedStartTime:
                type: string
                format: date-time
                description: 计划开始时间
              plannedEndTime:
                type: string
                format: date-time
                description: 计划结束时间
              workLocation:
                type: string
                description: 作业地点
              riskLevel:
                type: integer
                description: 风险等级
    responses:
      200:
        description: 创建成功
```

## 四、安全设计

### 4.1 身份认证与授权

#### 4.1.1 JWT令牌机制
- 使用JWT (JSON Web Token) 进行用户身份认证
- 令牌包含用户ID、角色信息、过期时间等
- 支持令牌刷新机制，提升用户体验

#### 4.1.2 权限控制
- 基于RBAC (Role-Based Access Control) 模型
- 支持细粒度的功能权限和数据权限控制
- 实现动态权限配置和实时生效

### 4.2 数据安全

#### 4.2.1 数据加密
- 敏感数据（密码、身份证号等）使用AES-256加密存储
- 数据传输使用HTTPS协议加密
- 数据库连接使用SSL加密

#### 4.2.2 数据脱敏
- 日志中的敏感信息自动脱敏
- 非生产环境数据脱敏处理
- 支持字段级别的脱敏配置

### 4.3 安全防护

#### 4.3.1 攻击防护
- SQL注入防护：使用参数化查询
- XSS攻击防护：输入输出过滤
- CSRF攻击防护：令牌验证机制
- 接口限流：防止恶意请求

#### 4.3.2 审计日志
- 记录所有用户操作日志
- 记录系统关键事件日志
- 日志信息包含：操作人、操作时间、操作内容、IP地址等
- 支持日志查询和分析功能

## 五、性能优化设计

### 5.1 缓存策略
- **Redis缓存**：缓存热点数据，减少数据库压力
- **本地缓存**：使用Caffeine缓存频繁访问的配置数据
- **CDN加速**：静态资源使用CDN分发

### 5.2 数据库优化
- **连接池优化**：使用HikariCP连接池
- **查询优化**：SQL语句优化，避免全表扫描
- **分页优化**：大数据量分页使用游标分页

### 5.3 前端优化
- **代码分割**：按路由进行代码分割，减少首屏加载时间
- **资源压缩**：JavaScript、CSS文件压缩
- **图片优化**：图片懒加载和格式优化

## 六、监控与运维

### 6.1 系统监控
- **应用监控**：使用Spring Boot Actuator监控应用状态
- **性能监控**：使用Micrometer收集性能指标
- **日志监控**：使用ELK Stack进行日志分析

### 6.2 告警机制
- **阈值告警**：CPU、内存、磁盘使用率告警
- **业务告警**：关键业务指标异常告警
- **故障告警**：系统故障自动告警通知

### 6.3 备份与恢复
- **数据备份**：每日自动全量备份，每小时增量备份
- **配置备份**：系统配置文件版本化管理
- **灾难恢复**：制定完整的灾难恢复预案
