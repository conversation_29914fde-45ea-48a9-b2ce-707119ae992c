<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>步骤2：作业前准备 - APP端</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        body {
            background: #f7f8fa;
            padding-bottom: 80px;
        }
        
        .mobile-container {
            max-width: 480px;
            margin: 0 auto;
            padding: 15px;
        }
        
        .checklist-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .checklist-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .checklist-item:last-child {
            border-bottom: none;
        }
        
        .checklist-checkbox {
            width: 24px;
            height: 24px;
            border: 2px solid #ddd;
            border-radius: 6px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .checklist-checkbox.checked {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }
        
        .checklist-content {
            flex: 1;
        }
        
        .checklist-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .checklist-desc {
            font-size: 14px;
            color: #666;
        }
        
        .checklist-action {
            margin-left: 10px;
        }
        
        .photo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }
        
        .photo-item {
            aspect-ratio: 1;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            background: #fafafa;
        }
        
        .photo-item:hover {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .photo-item.has-photo {
            border-style: solid;
            border-color: #4CAF50;
            background-size: cover;
            background-position: center;
        }
        
        .photo-icon {
            font-size: 32px;
            color: #999;
            margin-bottom: 8px;
        }
        
        .photo-text {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        .personnel-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .personnel-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .personnel-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4CAF50;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 12px;
        }
        
        .personnel-info {
            flex: 1;
        }
        
        .personnel-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        
        .personnel-role {
            font-size: 12px;
            color: #666;
        }
        
        .personnel-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-present {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-absent {
            background: #ffebee;
            color: #c62828;
        }
        
        .equipment-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-top: 15px;
        }
        
        .equipment-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #4CAF50;
        }
        
        .equipment-item.warning {
            border-left-color: #ff9800;
            background: #fff8e1;
        }
        
        .equipment-item.error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        
        .equipment-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .equipment-status {
            font-size: 12px;
            color: #666;
        }
        
        .progress-summary {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            position: sticky;
            top: 15px;
            z-index: 100;
        }
        
        .progress-text {
            text-align: center;
            margin-bottom: 10px;
            font-size: 14px;
            color: #666;
        }
        
        .fixed-bottom {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            max-width: 480px;
            margin: 0 auto;
        }
        
        .btn-full {
            flex: 1;
        }
        
        .signature-pad {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            background: white;
        }
        
        .signature-controls {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            border-top: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div>
                    <div class="header-title">步骤2：作业前准备</div>
                    <div class="header-subtitle">APP端 - 现场检查与人员确认</div>
                </div>
                <div class="header-user">
                    <div class="user-info">
                        <div class="user-name">李四</div>
                        <div class="user-role">安全员</div>
                    </div>
                    <div class="user-avatar">李</div>
                </div>
            </div>
        </div>
    </div>

    <div class="mobile-container">
        <!-- 进度概览 -->
        <div class="progress-summary">
            <div class="progress-text">准备工作进度：<span id="progressText">0/12</span> 项已完成</div>
            <div class="progress">
                <div class="progress-bar" id="progressBar" style="width: 0%"></div>
            </div>
        </div>

        <!-- 密闭性检查 -->
        <div class="checklist-card">
            <div class="card-title">仓房密闭性检查</div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">仓房门窗密闭检查</div>
                    <div class="checklist-desc">检查所有门窗是否完全关闭并密封良好</div>
                </div>
                <button class="btn btn-sm btn-outline checklist-action" onclick="takePhoto('door_window')">拍照</button>
            </div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">通风口密封检查</div>
                    <div class="checklist-desc">确认所有通风口已用塑料薄膜密封</div>
                </div>
                <button class="btn btn-sm btn-outline checklist-action" onclick="takePhoto('ventilation')">拍照</button>
            </div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">地面缝隙处理</div>
                    <div class="checklist-desc">检查地面缝隙是否已用密封胶处理</div>
                </div>
                <button class="btn btn-sm btn-outline checklist-action" onclick="takePhoto('floor_seal')">拍照</button>
            </div>
            
            <div class="photo-grid" id="sealingPhotos">
                <div class="photo-item" onclick="takePhoto('sealing_1')">
                    <div class="photo-icon">📷</div>
                    <div class="photo-text">密闭检查照片1</div>
                </div>
                <div class="photo-item" onclick="takePhoto('sealing_2')">
                    <div class="photo-icon">📷</div>
                    <div class="photo-text">密闭检查照片2</div>
                </div>
            </div>
        </div>

        <!-- 设备检查 -->
        <div class="checklist-card">
            <div class="card-title">设备状态检查</div>
            <div class="equipment-grid">
                <div class="equipment-item" onclick="checkEquipment('circulation_fan')">
                    <div class="equipment-name">环流风机</div>
                    <div class="equipment-status">点击检查</div>
                </div>
                <div class="equipment-item" onclick="checkEquipment('gas_detector')">
                    <div class="equipment-name">气体检测仪</div>
                    <div class="equipment-status">点击检查</div>
                </div>
                <div class="equipment-item" onclick="checkEquipment('temperature_sensor')">
                    <div class="equipment-name">温度传感器</div>
                    <div class="equipment-status">点击检查</div>
                </div>
                <div class="equipment-item" onclick="checkEquipment('humidity_sensor')">
                    <div class="equipment-name">湿度传感器</div>
                    <div class="equipment-status">点击检查</div>
                </div>
            </div>
            
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">设备运行测试</div>
                    <div class="checklist-desc">所有设备运行正常，参数显示准确</div>
                </div>
            </div>
        </div>

        <!-- 人员确认 -->
        <div class="personnel-card">
            <div class="card-title">作业人员确认</div>
            <div class="personnel-item">
                <div class="personnel-avatar">张</div>
                <div class="personnel-info">
                    <div class="personnel-name">张三</div>
                    <div class="personnel-role">作业负责人 | 证书编号：FUM001</div>
                </div>
                <div class="personnel-status status-present">已到场</div>
            </div>
            <div class="personnel-item">
                <div class="personnel-avatar">李</div>
                <div class="personnel-info">
                    <div class="personnel-name">李四</div>
                    <div class="personnel-role">安全员 | 证书编号：SAF002</div>
                </div>
                <div class="personnel-status status-present">已到场</div>
            </div>
            <div class="personnel-item">
                <div class="personnel-avatar">王</div>
                <div class="personnel-info">
                    <div class="personnel-name">王五</div>
                    <div class="personnel-role">操作人员 | 证书编号：OPR003</div>
                </div>
                <div class="personnel-status status-present">已到场</div>
            </div>
            
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">安全交底完成</div>
                    <div class="checklist-desc">已对所有作业人员进行安全技术交底</div>
                </div>
            </div>
        </div>

        <!-- 安全防护检查 -->
        <div class="checklist-card">
            <div class="card-title">安全防护检查</div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">防护用品配备</div>
                    <div class="checklist-desc">防毒面具、防护服、手套等配备齐全</div>
                </div>
                <button class="btn btn-sm btn-outline checklist-action" onclick="takePhoto('protection')">拍照</button>
            </div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">应急设备准备</div>
                    <div class="checklist-desc">急救箱、洗眼器、通讯设备等准备就绪</div>
                </div>
            </div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">警示标识设置</div>
                    <div class="checklist-desc">在作业区域设置警示标识和隔离带</div>
                </div>
                <button class="btn btn-sm btn-outline checklist-action" onclick="takePhoto('warning_signs')">拍照</button>
            </div>
        </div>

        <!-- 环境条件确认 -->
        <div class="checklist-card">
            <div class="card-title">环境条件确认</div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">天气条件适宜</div>
                    <div class="checklist-desc">无大风、降雨等不利天气条件</div>
                </div>
            </div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">温湿度条件</div>
                    <div class="checklist-desc">仓内温度20-25℃，湿度60-70%</div>
                </div>
            </div>
            <div class="checklist-item">
                <div class="checklist-checkbox" onclick="toggleCheck(this)"></div>
                <div class="checklist-content">
                    <div class="checklist-title">周边环境安全</div>
                    <div class="checklist-desc">作业区域周边无火源、无人员聚集</div>
                </div>
            </div>
        </div>

        <!-- 签名确认 -->
        <div class="checklist-card">
            <div class="card-title">签名确认</div>
            <canvas class="signature-pad" id="signaturePad" width="400" height="150"></canvas>
            <div class="signature-controls">
                <button class="btn btn-sm btn-secondary" onclick="clearSignature()">清除</button>
                <span style="font-size: 12px; color: #666;">安全员签名确认</span>
                <button class="btn btn-sm btn-primary" onclick="saveSignature()">保存</button>
            </div>
        </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="fixed-bottom">
        <div class="btn-group">
            <button class="btn btn-secondary btn-full" onclick="goBack()">返回</button>
            <button class="btn btn-primary btn-full" id="completeBtn" onclick="completePreparation()" disabled>完成准备</button>
        </div>
    </div>

    <script src="assets/common.js"></script>
    <script>
        let checkedItems = 0;
        const totalItems = 12;
        let signatureSaved = false;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateProgress();
            initializeSignaturePad();
        });

        // 切换检查项状态
        function toggleCheck(checkbox) {
            const isChecked = checkbox.classList.contains('checked');
            
            if (isChecked) {
                checkbox.classList.remove('checked');
                checkbox.innerHTML = '';
                checkedItems--;
            } else {
                checkbox.classList.add('checked');
                checkbox.innerHTML = '✓';
                checkedItems++;
            }
            
            updateProgress();
        }

        // 更新进度
        function updateProgress() {
            const progress = Math.round((checkedItems / totalItems) * 100);
            document.getElementById('progressText').textContent = `${checkedItems}/${totalItems}`;
            document.getElementById('progressBar').style.width = `${progress}%`;
            
            // 检查是否可以完成准备
            const completeBtn = document.getElementById('completeBtn');
            if (checkedItems === totalItems && signatureSaved) {
                completeBtn.disabled = false;
                completeBtn.classList.remove('btn-secondary');
                completeBtn.classList.add('btn-success');
            } else {
                completeBtn.disabled = true;
                completeBtn.classList.remove('btn-success');
                completeBtn.classList.add('btn-secondary');
            }
        }

        // 拍照功能
        function takePhoto(type) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.capture = 'camera';
            input.click();
            
            input.addEventListener('change', function() {
                if (input.files.length > 0) {
                    const file = input.files[0];
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        // 找到对应的照片区域并更新
                        const photoItems = document.querySelectorAll('.photo-item');
                        photoItems.forEach(item => {
                            if (item.onclick.toString().includes(type)) {
                                item.style.backgroundImage = `url(${e.target.result})`;
                                item.classList.add('has-photo');
                                item.innerHTML = '<div style="background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">已拍摄</div>';
                            }
                        });
                    };
                    
                    reader.readAsDataURL(file);
                    NotificationManager.success(`${getPhotoTypeName(type)}照片已保存`);
                }
            });
        }

        // 获取照片类型名称
        function getPhotoTypeName(type) {
            const names = {
                'door_window': '门窗密闭',
                'ventilation': '通风口密封',
                'floor_seal': '地面缝隙',
                'sealing_1': '密闭检查',
                'sealing_2': '密闭检查',
                'protection': '防护用品',
                'warning_signs': '警示标识'
            };
            return names[type] || '检查';
        }

        // 设备检查
        function checkEquipment(equipmentId) {
            const equipment = event.currentTarget;
            const statusElement = equipment.querySelector('.equipment-status');
            
            // 模拟检查过程
            statusElement.textContent = '检查中...';
            equipment.classList.add('warning');
            
            setTimeout(() => {
                const isNormal = Math.random() > 0.2; // 80%概率正常
                
                if (isNormal) {
                    equipment.classList.remove('warning');
                    equipment.classList.add('normal');
                    statusElement.textContent = '正常';
                    statusElement.style.color = '#2e7d32';
                } else {
                    equipment.classList.remove('warning');
                    equipment.classList.add('error');
                    statusElement.textContent = '异常';
                    statusElement.style.color = '#c62828';
                }
                
                NotificationManager.info(`${getEquipmentName(equipmentId)}检查完成`);
            }, 1500);
        }

        // 获取设备名称
        function getEquipmentName(equipmentId) {
            const names = {
                'circulation_fan': '环流风机',
                'gas_detector': '气体检测仪',
                'temperature_sensor': '温度传感器',
                'humidity_sensor': '湿度传感器'
            };
            return names[equipmentId] || '设备';
        }

        // 初始化签名板
        function initializeSignaturePad() {
            const canvas = document.getElementById('signaturePad');
            const ctx = canvas.getContext('2d');
            let isDrawing = false;
            let lastX = 0;
            let lastY = 0;

            function getEventPos(e) {
                const rect = canvas.getBoundingClientRect();
                const clientX = e.clientX || (e.touches && e.touches[0].clientX);
                const clientY = e.clientY || (e.touches && e.touches[0].clientY);
                return {
                    x: (clientX - rect.left) * (canvas.width / rect.width),
                    y: (clientY - rect.top) * (canvas.height / rect.height)
                };
            }

            function startDrawing(e) {
                isDrawing = true;
                const pos = getEventPos(e);
                lastX = pos.x;
                lastY = pos.y;
            }

            function draw(e) {
                if (!isDrawing) return;
                
                const pos = getEventPos(e);
                ctx.beginPath();
                ctx.moveTo(lastX, lastY);
                ctx.lineTo(pos.x, pos.y);
                ctx.strokeStyle = '#333';
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                ctx.stroke();
                
                lastX = pos.x;
                lastY = pos.y;
            }

            function stopDrawing() {
                isDrawing = false;
            }

            // 鼠标事件
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);

            // 触摸事件
            canvas.addEventListener('touchstart', function(e) {
                e.preventDefault();
                startDrawing(e);
            });
            canvas.addEventListener('touchmove', function(e) {
                e.preventDefault();
                draw(e);
            });
            canvas.addEventListener('touchend', function(e) {
                e.preventDefault();
                stopDrawing();
            });
        }

        // 清除签名
        function clearSignature() {
            const canvas = document.getElementById('signaturePad');
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            signatureSaved = false;
            updateProgress();
        }

        // 保存签名
        function saveSignature() {
            const canvas = document.getElementById('signaturePad');
            const ctx = canvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            
            // 检查是否有签名内容
            let hasSignature = false;
            for (let i = 0; i < data.length; i += 4) {
                if (data[i + 3] > 0) { // alpha channel
                    hasSignature = true;
                    break;
                }
            }
            
            if (!hasSignature) {
                NotificationManager.warning('请先进行签名');
                return;
            }
            
            signatureSaved = true;
            updateProgress();
            NotificationManager.success('签名已保存');
        }

        // 完成准备工作
        function completePreparation() {
            if (checkedItems !== totalItems) {
                NotificationManager.error('请完成所有检查项');
                return;
            }
            
            if (!signatureSaved) {
                NotificationManager.error('请完成签名确认');
                return;
            }
            
            // 保存准备工作数据
            const preparationData = {
                checkedItems: checkedItems,
                totalItems: totalItems,
                completedAt: new Date().toISOString(),
                signatureSaved: signatureSaved,
                status: 'completed'
            };
            
            workflowManager.completeStep(2, preparationData);
            
            NotificationManager.success('作业前准备已完成！');
            
            setTimeout(() => {
                window.location.href = 'step3.html';
            }, 2000);
        }

        // 返回
        function goBack() {
            window.location.href = 'index.html';
        }

        // 防止页面缩放
        document.addEventListener('touchstart', function(event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        });

        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
