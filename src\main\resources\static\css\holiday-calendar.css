/* 日历视图节假日管理系统样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

/* 侧边栏样式 */
.list-group-item {
    border: none;
    border-radius: 0;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 图例样式 */
.legend-item {
    display: flex;
    align-items: center;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 8px;
    display: inline-block;
}

.legend-color.weekend {
    background-color: #6c757d;
}

.legend-color.legal-holiday {
    background-color: #dc3545;
}

.legend-color.adjust-workday {
    background-color: #fd7e14;
}

.legend-color.custom-holiday {
    background-color: #6f42c1;
}

.legend-color.today {
    background-color: #198754;
}

.legend-text {
    font-size: 0.875rem;
    color: #495057;
}

/* 统计卡片样式 */
.card-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.card-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
}

/* 年历容器样式 */
.year-calendar-container {
    padding: 1rem 0;
}

.month-calendar {
    margin-bottom: 2rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    overflow: hidden;
}

.month-header {
    background-color: #5cb85c;
    color: white;
    text-align: center;
    padding: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0;
}

.weekday-header {
    background-color: #f8f9fa;
    text-align: center;
    padding: 0.5rem;
    font-weight: 600;
    font-size: 0.875rem;
    border-bottom: 1px solid #dee2e6;
    color: #495057;
}

.calendar-day {
    min-height: 60px;
    border: 1px solid #e9ecef;
    padding: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    background-color: white;
}

.calendar-day:hover {
    background-color: #f8f9fa;
    transform: scale(1.02);
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.day-number {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.day-label {
    font-size: 0.75rem;
    text-align: center;
    line-height: 1.2;
    font-weight: 500;
}

/* 不同类型日期的样式 */
.calendar-day.workday {
    background-color: #ffffff;
    color: #333333;
}

.calendar-day.weekend {
    background-color: #f8f9fa;
    color: #6c757d;
}

.calendar-day.weekend .day-label {
    color: #6c757d;
}

.calendar-day.legal-holiday {
    background-color: #ffebee;
    color: #d32f2f;
    border-color: #f8bbd9;
}

.calendar-day.legal-holiday .day-number {
    color: #d32f2f;
}

.calendar-day.legal-holiday .day-label {
    color: #d32f2f;
}

.calendar-day.adjust-workday {
    background-color: #e8f5e8;
    color: #2e7d32;
    border-color: #a5d6a7;
}

.calendar-day.adjust-workday .day-number {
    color: #2e7d32;
}

.calendar-day.adjust-workday .day-label {
    color: #2e7d32;
}

.calendar-day.custom-holiday {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border-color: #ce93d8;
}

.calendar-day.custom-holiday .day-number {
    color: #7b1fa2;
}

.calendar-day.custom-holiday .day-label {
    color: #7b1fa2;
}

.calendar-day.today {
    box-shadow: inset 0 0 0 2px #198754;
    font-weight: bold;
}

.calendar-day.today .day-number {
    background-color: #198754;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

/* 其他月份的日期 */
.calendar-day.other-month {
    color: #ccc;
    background-color: #fafafa;
}

.calendar-day.other-month .day-label {
    color: #ccc;
}

/* 响应式布局 */
@media (min-width: 1200px) {
    .year-calendar-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }

    .month-calendar {
        margin-bottom: 0;
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .year-calendar-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .month-calendar {
        margin-bottom: 0;
    }
}

@media (max-width: 767px) {
    .calendar-day {
        min-height: 50px;
        padding: 0.125rem;
    }

    .day-number {
        font-size: 0.875rem;
    }

    .day-label {
        font-size: 0.625rem;
    }
}

/* 月历网格布局样式 - 类似参考图片 */

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 模态框样式 */
.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* 表单样式 */
.form-label {
    font-weight: 500;
    color: #495057;
}

.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成功/错误消息样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .col-md-2 {
        margin-bottom: 1rem;
    }

    #calendar {
        min-height: 400px;
    }

    .fc-toolbar {
        flex-direction: column;
        gap: 0.5rem;
    }

    .fc-toolbar-title {
        font-size: 1.25rem;
    }

    .fc-button {
        font-size: 0.875rem;
        padding: 0.25rem 0.5rem;
    }

    .card-title {
        font-size: 1.5rem;
    }

    .legend-text {
        font-size: 0.75rem;
    }
}

@media (max-width: 576px) {
    .fc-daygrid-day {
        min-height: 60px;
    }

    .fc-daygrid-day-number {
        font-size: 0.75rem;
    }

    .holiday-name {
        font-size: 0.6rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 日历事件样式 */
.fc-event {
    border: none;
    border-radius: 3px;
    font-size: 0.75rem;
    padding: 1px 3px;
}

.fc-event-title {
    font-weight: 500;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}