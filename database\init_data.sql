-- 粮库风险监测系统 - 初始数据
USE grain_risk_monitor;

-- 插入2024年法定节假日数据
INSERT INTO holiday_config (holiday_date, holiday_name, holiday_type, is_workday, year_num, remark) VALUES
('2024-01-01', '元旦', 1, 0, 2024, '元旦节'),
('2024-02-10', '春节', 1, 0, 2024, '春节假期'),
('2024-02-11', '春节', 1, 0, 2024, '春节假期'),
('2024-02-12', '春节', 1, 0, 2024, '春节假期'),
('2024-02-13', '春节', 1, 0, 2024, '春节假期'),
('2024-02-14', '春节', 1, 0, 2024, '春节假期'),
('2024-02-15', '春节', 1, 0, 2024, '春节假期'),
('2024-02-16', '春节', 1, 0, 2024, '春节假期'),
('2024-02-17', '春节', 1, 0, 2024, '春节假期'),
('2024-04-04', '清明节', 1, 0, 2024, '清明节'),
('2024-04-05', '清明节', 1, 0, 2024, '清明节'),
('2024-04-06', '清明节', 1, 0, 2024, '清明节'),
('2024-05-01', '劳动节', 1, 0, 2024, '劳动节'),
('2024-05-02', '劳动节', 1, 0, 2024, '劳动节'),
('2024-05-03', '劳动节', 1, 0, 2024, '劳动节'),
('2024-05-04', '劳动节', 1, 0, 2024, '劳动节'),
('2024-05-05', '劳动节', 1, 0, 2024, '劳动节'),
('2024-06-10', '端午节', 1, 0, 2024, '端午节'),
('2024-09-15', '中秋节', 1, 0, 2024, '中秋节'),
('2024-09-16', '中秋节', 1, 0, 2024, '中秋节'),
('2024-09-17', '中秋节', 1, 0, 2024, '中秋节'),
('2024-10-01', '国庆节', 1, 0, 2024, '国庆节'),
('2024-10-02', '国庆节', 1, 0, 2024, '国庆节'),
('2024-10-03', '国庆节', 1, 0, 2024, '国庆节'),
('2024-10-04', '国庆节', 1, 0, 2024, '国庆节'),
('2024-10-05', '国庆节', 1, 0, 2024, '国庆节'),
('2024-10-06', '国庆节', 1, 0, 2024, '国庆节'),
('2024-10-07', '国庆节', 1, 0, 2024, '国庆节');

-- 插入调休工作日数据
INSERT INTO holiday_config (holiday_date, holiday_name, holiday_type, is_workday, year_num, remark) VALUES
('2024-02-04', '春节调休', 2, 1, 2024, '春节调休上班'),
('2024-02-18', '春节调休', 2, 1, 2024, '春节调休上班'),
('2024-04-07', '清明节调休', 2, 1, 2024, '清明节调休上班'),
('2024-04-28', '劳动节调休', 2, 1, 2024, '劳动节调休上班'),
('2024-05-11', '劳动节调休', 2, 1, 2024, '劳动节调休上班'),
('2024-09-14', '中秋节调休', 2, 1, 2024, '中秋节调休上班'),
('2024-09-29', '国庆节调休', 2, 1, 2024, '国庆节调休上班'),
('2024-10-12', '国庆节调休', 2, 1, 2024, '国庆节调休上班');

-- 插入风险类型配置数据
INSERT INTO risk_type_config (risk_type_code, risk_type_name, default_dispose_days, risk_level, description) VALUES
('TEMP_ABNORMAL', '粮温异常', 3, 2, '粮食温度超出正常范围'),
('HUMIDITY_HIGH', '湿度过高', 2, 1, '仓库湿度超标'),
('PEST_FOUND', '虫害发现', 5, 3, '发现害虫'),
('MOLD_RISK', '霉变风险', 1, 3, '粮食有霉变风险'),
('EQUIPMENT_FAULT', '设备故障', 2, 2, '监测设备故障'),
('SECURITY_BREACH', '安全隐患', 1, 3, '发现安全隐患');

-- 插入系统配置数据
INSERT INTO system_config (config_key, config_value, config_desc, config_type) VALUES
('WORKDAY_START_HOUR', '8', '工作日开始时间（小时）', 'INTEGER'),
('WORKDAY_END_HOUR', '18', '工作日结束时间（小时）', 'INTEGER'),
('HOLIDAY_CACHE_EXPIRE', '86400', '节假日缓存过期时间（秒）', 'INTEGER'),
('DEFAULT_DISPOSE_DAYS', '3', '默认处置时限（工作日）', 'INTEGER'),
('OVERTIME_ALERT_ENABLED', 'true', '是否启用超时提醒', 'BOOLEAN');