<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时监控大屏 - 高危作业标准化流程管理系统</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        body {
            background: #0a0e27;
            color: #ffffff;
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            overflow-x: hidden;
        }
        
        .monitor-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.3);
        }
        
        .monitor-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .monitor-subtitle {
            font-size: 16px;
            opacity: 0.8;
        }
        
        .monitor-container {
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 20px;
            height: calc(100vh - 120px);
        }
        
        .monitor-panel {
            background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .monitor-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00d4ff, #00ff88, #ffaa00, #ff0080);
            animation: rainbow 3s linear infinite;
        }
        
        @keyframes rainbow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #00d4ff;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .overview-item {
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .overview-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 8px;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .overview-label {
            font-size: 12px;
            color: #cccccc;
            text-transform: uppercase;
        }
        
        .work-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .work-item {
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #00d4ff;
            transition: all 0.3s;
        }
        
        .work-item:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        
        .work-item.active {
            border-left-color: #00ff88;
            background: rgba(0,255,136,0.1);
        }
        
        .work-item.completed {
            border-left-color: #888;
            opacity: 0.7;
        }
        
        .work-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .work-meta {
            font-size: 12px;
            color: #cccccc;
            display: flex;
            justify-content: space-between;
        }
        
        .realtime-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .realtime-item {
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255,255,255,0.1);
            position: relative;
        }
        
        .realtime-item.warning {
            border-color: #ffaa00;
            background: rgba(255,170,0,0.1);
        }
        
        .realtime-item.danger {
            border-color: #ff0080;
            background: rgba(255,0,128,0.1);
            animation: pulse-danger 2s infinite;
        }
        
        @keyframes pulse-danger {
            0%, 100% { box-shadow: 0 0 0 0 rgba(255,0,128,0.7); }
            50% { box-shadow: 0 0 0 10px rgba(255,0,128,0); }
        }
        
        .realtime-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .realtime-label {
            font-size: 12px;
            color: #cccccc;
        }
        
        .realtime-trend {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 12px;
        }
        
        .trend-up {
            color: #ff0080;
        }
        
        .trend-down {
            color: #00ff88;
        }
        
        .alert-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .alert-item {
            background: rgba(255,0,128,0.1);
            border: 1px solid #ff0080;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            font-size: 12px;
        }
        
        .alert-time {
            color: #cccccc;
            margin-bottom: 5px;
        }
        
        .alert-message {
            color: #ffffff;
        }
        
        .chart-container {
            height: 200px;
            background: rgba(255,255,255,0.05);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .chart-placeholder {
            text-align: center;
            color: #666;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-normal {
            background: #00ff88;
            animation: pulse-normal 2s infinite;
        }
        
        .status-warning {
            background: #ffaa00;
            animation: pulse-warning 2s infinite;
        }
        
        .status-danger {
            background: #ff0080;
            animation: pulse-danger 2s infinite;
        }
        
        @keyframes pulse-normal {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @keyframes pulse-warning {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }
        
        .time-display {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 18px;
            font-weight: bold;
            color: #00d4ff;
        }
        
        .fullscreen-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0,212,255,0.3);
            transition: all 0.3s;
        }
        
        .fullscreen-btn:hover {
            transform: scale(1.1);
        }
        
        @media (max-width: 1200px) {
            .monitor-container {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .monitor-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="monitor-header">
        <div class="monitor-title">高危作业实时监控中心</div>
        <div class="monitor-subtitle">环流熏蒸作业全流程监控 | 实时数据更新</div>
        <div class="time-display" id="currentTime"></div>
    </div>

    <div class="monitor-container">
        <!-- 作业概览 -->
        <div class="monitor-panel">
            <div class="panel-title">📊 作业概览</div>
            <div class="overview-grid">
                <div class="overview-item">
                    <div class="overview-number" id="totalWorks">3</div>
                    <div class="overview-label">总作业数</div>
                </div>
                <div class="overview-item">
                    <div class="overview-number" id="activeWorks">1</div>
                    <div class="overview-label">进行中</div>
                </div>
                <div class="overview-item">
                    <div class="overview-number" id="completedWorks">1</div>
                    <div class="overview-label">已完成</div>
                </div>
                <div class="overview-item">
                    <div class="overview-number" id="pendingWorks">1</div>
                    <div class="overview-label">待审批</div>
                </div>
            </div>
        </div>

        <!-- 作业列表 -->
        <div class="monitor-panel">
            <div class="panel-title">📋 作业状态</div>
            <div class="work-list">
                <div class="work-item active">
                    <div class="work-title">
                        <span class="status-indicator status-normal"></span>
                        1号仓小麦环流熏蒸作业
                    </div>
                    <div class="work-meta">
                        <span>FUM-2024-001</span>
                        <span>步骤3/7</span>
                    </div>
                </div>
                <div class="work-item">
                    <div class="work-title">
                        <span class="status-indicator status-warning"></span>
                        3号仓玉米环流熏蒸作业
                    </div>
                    <div class="work-meta">
                        <span>FUM-2024-002</span>
                        <span>待审批</span>
                    </div>
                </div>
                <div class="work-item completed">
                    <div class="work-title">
                        <span class="status-indicator status-normal"></span>
                        5号仓大豆环流熏蒸作业
                    </div>
                    <div class="work-meta">
                        <span>FUM-2024-003</span>
                        <span>已完成</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时监测数据 -->
        <div class="monitor-panel">
            <div class="panel-title">🌡️ 实时监测</div>
            <div class="realtime-grid">
                <div class="realtime-item">
                    <div class="realtime-trend trend-up">↗</div>
                    <div class="realtime-value" id="monitorTemp">23.5°C</div>
                    <div class="realtime-label">仓内温度</div>
                </div>
                <div class="realtime-item">
                    <div class="realtime-trend trend-down">↘</div>
                    <div class="realtime-value" id="monitorHumidity">65%</div>
                    <div class="realtime-label">相对湿度</div>
                </div>
                <div class="realtime-item danger">
                    <div class="realtime-trend trend-up">↗</div>
                    <div class="realtime-value" id="monitorPH3">850</div>
                    <div class="realtime-label">PH3浓度(mg/m³)</div>
                </div>
                <div class="realtime-item">
                    <div class="realtime-trend trend-down">↘</div>
                    <div class="realtime-value" id="monitorO2">18.5%</div>
                    <div class="realtime-label">氧气浓度</div>
                </div>
            </div>
        </div>

        <!-- 安全状态 -->
        <div class="monitor-panel">
            <div class="panel-title">🛡️ 安全状态</div>
            <div class="overview-grid">
                <div class="overview-item">
                    <div class="overview-number" style="color: #00ff88;">5</div>
                    <div class="overview-label">在岗人员</div>
                </div>
                <div class="overview-item">
                    <div class="overview-number" style="color: #00d4ff;">3</div>
                    <div class="overview-label">设备正常</div>
                </div>
                <div class="overview-item">
                    <div class="overview-number" style="color: #ffaa00;">1</div>
                    <div class="overview-label">预警信息</div>
                </div>
                <div class="overview-item">
                    <div class="overview-number" style="color: #ff0080;">0</div>
                    <div class="overview-label">紧急事件</div>
                </div>
            </div>
        </div>

        <!-- 报警信息 -->
        <div class="monitor-panel">
            <div class="panel-title">⚠️ 报警信息</div>
            <div class="alert-list">
                <div class="alert-item">
                    <div class="alert-time">2024-01-16 14:35:20</div>
                    <div class="alert-message">1号仓PH3浓度超过安全阈值，当前值：850mg/m³</div>
                </div>
                <div class="alert-item">
                    <div class="alert-time">2024-01-16 13:20:15</div>
                    <div class="alert-message">3号仓温度传感器通信异常，请检查设备连接</div>
                </div>
                <div class="alert-item">
                    <div class="alert-time">2024-01-16 12:45:30</div>
                    <div class="alert-message">2号仓环流风机运行时间超过8小时，建议检查设备状态</div>
                </div>
            </div>
        </div>

        <!-- 数据趋势图 -->
        <div class="monitor-panel">
            <div class="panel-title">📈 数据趋势</div>
            <div class="chart-container">
                <div class="chart-placeholder">
                    <div style="font-size: 48px; margin-bottom: 15px;">📊</div>
                    <div>PH3浓度变化趋势图</div>
                    <div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">实时数据更新中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 全屏按钮 -->
    <button class="fullscreen-btn" onclick="toggleFullscreen()" title="全屏显示">
        ⛶
    </button>

    <script src="assets/common.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            initializeRealTimeData();
            startDataUpdate();
            
            // 每秒更新时间
            setInterval(updateCurrentTime, 1000);
        });

        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        // 初始化实时数据
        function initializeRealTimeData() {
            realTimeDataManager.subscribe(data => {
                // 更新监测数据显示
                document.getElementById('monitorTemp').textContent = Utils.formatNumber(data.temperature, 1) + '°C';
                document.getElementById('monitorHumidity').textContent = Math.round(data.humidity) + '%';
                document.getElementById('monitorPH3').textContent = Math.round(data.ph3Concentration);
                document.getElementById('monitorO2').textContent = Utils.formatNumber(data.o2Concentration, 1) + '%';
                
                // 更新PH3浓度状态
                const ph3Element = document.getElementById('monitorPH3').parentElement;
                if (data.ph3Concentration > 900) {
                    ph3Element.className = 'realtime-item danger';
                } else if (data.ph3Concentration > 850) {
                    ph3Element.className = 'realtime-item warning';
                } else {
                    ph3Element.className = 'realtime-item';
                }
                
                // 检查是否需要添加新的报警信息
                if (data.ph3Concentration > 900 && !window.lastAlertTime) {
                    addNewAlert('PH3浓度严重超标，当前值：' + Math.round(data.ph3Concentration) + 'mg/m³');
                    window.lastAlertTime = Date.now();
                } else if (Date.now() - (window.lastAlertTime || 0) > 60000) {
                    // 重置报警时间，避免频繁报警
                    window.lastAlertTime = null;
                }
            });
        }

        // 添加新的报警信息
        function addNewAlert(message) {
            const alertList = document.querySelector('.alert-list');
            const alertItem = document.createElement('div');
            alertItem.className = 'alert-item';
            
            const now = new Date();
            const timeString = Utils.formatDateTime(now, 'YYYY-MM-DD HH:mm:ss');
            
            alertItem.innerHTML = `
                <div class="alert-time">${timeString}</div>
                <div class="alert-message">${message}</div>
            `;
            
            // 插入到列表顶部
            alertList.insertBefore(alertItem, alertList.firstChild);
            
            // 限制报警条数
            const alertItems = alertList.querySelectorAll('.alert-item');
            if (alertItems.length > 10) {
                alertList.removeChild(alertItems[alertItems.length - 1]);
            }
            
            // 添加闪烁效果
            alertItem.style.animation = 'pulse-danger 1s ease-in-out 3';
        }

        // 开始数据更新
        function startDataUpdate() {
            // 模拟作业数据更新
            setInterval(() => {
                // 随机更新一些统计数据
                const totalWorks = Math.floor(Math.random() * 2) + 3; // 3-4
                const activeWorks = Math.floor(Math.random() * 2) + 1; // 1-2
                const completedWorks = Math.floor(Math.random() * 2) + 1; // 1-2
                const pendingWorks = totalWorks - activeWorks - completedWorks;
                
                document.getElementById('totalWorks').textContent = totalWorks;
                document.getElementById('activeWorks').textContent = activeWorks;
                document.getElementById('completedWorks').textContent = completedWorks;
                document.getElementById('pendingWorks').textContent = Math.max(0, pendingWorks);
            }, 10000); // 每10秒更新一次
        }

        // 全屏切换
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('无法进入全屏模式:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    } else {
                        window.close();
                    }
                    break;
                case 'r':
                case 'R':
                    if (e.ctrlKey) {
                        e.preventDefault();
                        location.reload();
                    }
                    break;
            }
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                // 页面隐藏时可以暂停一些更新
                console.log('监控页面已隐藏');
            } else {
                // 页面显示时恢复更新
                console.log('监控页面已显示');
            }
        });

        // 添加一些动态效果
        setInterval(() => {
            // 随机闪烁一些状态指示器
            const indicators = document.querySelectorAll('.status-indicator');
            indicators.forEach(indicator => {
                if (Math.random() > 0.8) {
                    indicator.style.animation = 'pulse-normal 0.5s ease-in-out';
                    setTimeout(() => {
                        indicator.style.animation = '';
                    }, 500);
                }
            });
        }, 3000);

        console.log('实时监控大屏已启动');
    </script>
</body>
</html>
