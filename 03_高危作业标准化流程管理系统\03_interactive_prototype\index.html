<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高危作业标准化流程管理系统</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
            margin-top: 20px;
        }
        
        .work-list {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .work-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .work-item:hover {
            border-color: #4CAF50;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
            transform: translateY(-1px);
        }
        
        .work-item.active {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .work-info {
            flex: 1;
        }
        
        .work-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .work-meta {
            font-size: 12px;
            color: #666;
            display: flex;
            gap: 15px;
        }
        
        .work-progress {
            width: 120px;
            margin: 0 20px;
        }
        
        .progress-text {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
            text-align: center;
        }
        
        .work-actions {
            display: flex;
            gap: 8px;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            border-left: 4px solid #4CAF50;
        }
        
        .stat-card.warning {
            border-left-color: #ff9800;
        }
        
        .stat-card.danger {
            border-left-color: #f44336;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .action-btn {
            width: 100%;
            margin-bottom: 10px;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .action-icon {
            font-size: 18px;
            width: 24px;
        }
        
        .role-switcher {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .role-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin-bottom: 15px;
        }
        
        .current-user {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            font-size: 14px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #4CAF50;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .platform-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .platform-web {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .platform-app {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .platform-both {
            background: #fff3e0;
            color: #f57c00;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .work-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .work-progress {
                width: 100%;
                margin: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div>
                    <div class="header-title">高危作业标准化流程管理系统</div>
                    <div class="header-subtitle">环流熏蒸作业管理平台</div>
                </div>
                <div class="header-user">
                    <div class="user-info">
                        <div class="user-name" id="currentUserName">张三</div>
                        <div class="user-role" id="currentUserRole">作业负责人</div>
                    </div>
                    <div class="user-avatar" id="userAvatar">张</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 工作流程概览 -->
        <div class="workflow-container">
            <h3>环流熏蒸作业流程概览</h3>
            <div class="workflow-steps" id="workflowSteps">
                <!-- 动态生成 -->
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- 主要内容区域 -->
            <div class="work-list">
                <div class="card-header">
                    <div>
                        <div class="card-title">作业申请列表</div>
                        <div class="card-subtitle">当前共有 3 个作业申请</div>
                    </div>
                    <button class="btn btn-primary" onclick="createNewWork()">
                        <span class="action-icon">+</span>
                        新建申请
                    </button>
                </div>

                <div id="workList">
                    <!-- 作业列表项 -->
                    <div class="work-item active" onclick="selectWork('FUM-2024-001')">
                        <div class="user-avatar">1</div>
                        <div class="work-info">
                            <div class="work-title">1号仓小麦环流熏蒸作业</div>
                            <div class="work-meta">
                                <span>申请编号：FUM-2024-001</span>
                                <span>申请人：张三</span>
                                <span>申请时间：2024-01-15 09:30</span>
                                <span class="platform-indicator platform-app">APP端</span>
                            </div>
                        </div>
                        <div class="work-progress">
                            <div class="progress-text">进度：43% (3/7)</div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 43%"></div>
                            </div>
                        </div>
                        <div class="work-actions">
                            <span class="badge badge-info">进行中</span>
                            <button class="btn btn-sm btn-primary" onclick="enterWorkflow('FUM-2024-001')">进入</button>
                        </div>
                    </div>

                    <div class="work-item" onclick="selectWork('FUM-2024-002')">
                        <div class="user-avatar">2</div>
                        <div class="work-info">
                            <div class="work-title">3号仓玉米环流熏蒸作业</div>
                            <div class="work-meta">
                                <span>申请编号：FUM-2024-002</span>
                                <span>申请人：李四</span>
                                <span>申请时间：2024-01-16 14:20</span>
                                <span class="platform-indicator platform-web">WEB端</span>
                            </div>
                        </div>
                        <div class="work-progress">
                            <div class="progress-text">进度：14% (1/7)</div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 14%"></div>
                            </div>
                        </div>
                        <div class="work-actions">
                            <span class="badge badge-warning">待审批</span>
                            <button class="btn btn-sm btn-secondary" onclick="enterWorkflow('FUM-2024-002')">查看</button>
                        </div>
                    </div>

                    <div class="work-item" onclick="selectWork('FUM-2024-003')">
                        <div class="user-avatar">3</div>
                        <div class="work-info">
                            <div class="work-title">5号仓大豆环流熏蒸作业</div>
                            <div class="work-meta">
                                <span>申请编号：FUM-2024-003</span>
                                <span>申请人：王五</span>
                                <span>申请时间：2024-01-17 10:15</span>
                                <span class="platform-indicator platform-both">协同</span>
                            </div>
                        </div>
                        <div class="work-progress">
                            <div class="progress-text">进度：100% (7/7)</div>
                            <div class="progress">
                                <div class="progress-bar" style="width: 100%"></div>
                            </div>
                        </div>
                        <div class="work-actions">
                            <span class="badge badge-success">已完成</span>
                            <button class="btn btn-sm btn-outline" onclick="enterWorkflow('FUM-2024-003')">查看</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 统计数据 -->
                <div class="card">
                    <div class="card-title">作业统计</div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">3</div>
                            <div class="stat-label">总作业数</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-number">1</div>
                            <div class="stat-label">进行中</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">1</div>
                            <div class="stat-label">待审批</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">1</div>
                            <div class="stat-label">已完成</div>
                        </div>
                    </div>
                </div>

                <!-- 角色切换 -->
                <div class="role-switcher">
                    <div class="card-title">角色切换</div>
                    <select class="role-select" id="roleSelect" onchange="switchRole()">
                        <option value="work_leader">作业负责人</option>
                        <option value="safety_officer">安全员</option>
                        <option value="operator">操作人员</option>
                        <option value="inspector">检验员</option>
                        <option value="safety_manager">安全主管</option>
                        <option value="director">粮库主任</option>
                    </select>
                    <div class="current-user">
                        <strong>当前用户：</strong><span id="currentRoleDisplay">作业负责人</span><br>
                        <strong>权限级别：</strong><span id="currentPermissionLevel">6</span>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="quick-actions">
                    <div class="card-title">快捷操作</div>
                    <button class="action-btn btn btn-outline" onclick="openMonitor()">
                        <span class="action-icon">📊</span>
                        实时监控大屏
                    </button>
                    <button class="action-btn btn btn-outline" onclick="openReports()">
                        <span class="action-icon">📋</span>
                        作业报告查询
                    </button>
                    <button class="action-btn btn btn-outline" onclick="openSettings()">
                        <span class="action-icon">⚙️</span>
                        系统设置
                    </button>
                    <button class="action-btn btn btn-outline" onclick="openHelp()">
                        <span class="action-icon">❓</span>
                        帮助文档
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/common.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeWorkflowSteps();
            initializeRealTimeData();
            updateUserInterface();
        });

        // 初始化工作流程步骤
        function initializeWorkflowSteps() {
            const stepsContainer = document.getElementById('workflowSteps');
            stepsContainer.innerHTML = '';

            WORKFLOW_STEPS.forEach(step => {
                const stepElement = document.createElement('div');
                stepElement.className = `workflow-step ${step.status}`;
                stepElement.onclick = () => navigateToStep(step.id);
                
                stepElement.innerHTML = `
                    <div class="step-circle">${step.status === 'completed' ? '✓' : step.id}</div>
                    <div class="step-title">${step.name}</div>
                    <div class="step-time">${getStepTime(step)}</div>
                    <div class="platform-indicator platform-${step.platform}">${getPlatformText(step.platform)}</div>
                `;
                
                stepsContainer.appendChild(stepElement);
            });
        }

        // 获取步骤时间显示
        function getStepTime(step) {
            const stepData = workflowManager.getStepData(step.id);
            if (step.status === 'completed' && stepData.completedAt) {
                return Utils.formatDateTime(stepData.completedAt, 'MM-DD HH:mm');
            } else if (step.status === 'active') {
                return '进行中...';
            } else {
                return '待执行';
            }
        }

        // 获取平台文本
        function getPlatformText(platform) {
            const texts = {
                'web': 'WEB端',
                'app': 'APP端',
                'both': '协同'
            };
            return texts[platform] || platform;
        }

        // 导航到指定步骤
        function navigateToStep(stepId) {
            const currentRole = document.getElementById('roleSelect').value;
            
            if (!workflowManager.canAccessStep(stepId, currentRole)) {
                NotificationManager.warning('您没有权限访问此步骤或步骤尚未解锁');
                return;
            }

            const step = WORKFLOW_STEPS.find(s => s.id === stepId);
            if (step) {
                window.location.href = `step${stepId}.html`;
            }
        }

        // 选择作业
        function selectWork(workId) {
            // 移除其他选中状态
            document.querySelectorAll('.work-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('active');
            
            // 更新工作流程数据
            if (workId === 'FUM-2024-001') {
                // 当前活跃的作业
                workflowManager.setCurrentStep(3);
            }
            
            NotificationManager.info(`已选择作业：${workId}`);
        }

        // 进入工作流程
        function enterWorkflow(workId) {
            event.stopPropagation();
            
            if (workId === 'FUM-2024-001') {
                // 进入当前步骤
                const currentStep = workflowManager.getCurrentStep();
                window.location.href = `step${currentStep}.html`;
            } else {
                NotificationManager.info(`查看作业：${workId}`);
            }
        }

        // 创建新作业
        function createNewWork() {
            NotificationManager.info('新建作业申请功能开发中...');
        }

        // 角色切换
        function switchRole() {
            const roleSelect = document.getElementById('roleSelect');
            const selectedRole = roleSelect.value;
            const roleInfo = USER_ROLES[selectedRole];
            
            if (roleInfo) {
                document.getElementById('currentUserName').textContent = getRoleUserName(selectedRole);
                document.getElementById('currentUserRole').textContent = roleInfo.name;
                document.getElementById('currentRoleDisplay').textContent = roleInfo.name;
                document.getElementById('currentPermissionLevel').textContent = roleInfo.level;
                document.getElementById('userAvatar').textContent = getRoleUserName(selectedRole).charAt(0);
                
                // 更新界面权限
                updateUserInterface();
                
                NotificationManager.success(`已切换到：${roleInfo.name}`);
            }
        }

        // 获取角色对应的用户名
        function getRoleUserName(role) {
            const names = {
                'work_leader': '张三',
                'safety_officer': '李四',
                'operator': '王五',
                'inspector': '赵六',
                'safety_manager': '钱七',
                'director': '孙八'
            };
            return names[role] || '用户';
        }

        // 更新用户界面
        function updateUserInterface() {
            const currentRole = document.getElementById('roleSelect').value;
            const userLevel = USER_ROLES[currentRole]?.level || 0;
            
            // 根据权限级别显示/隐藏功能
            const actionButtons = document.querySelectorAll('.action-btn');
            actionButtons.forEach(btn => {
                const action = btn.textContent.trim();
                if (action.includes('系统设置') && userLevel < 8) {
                    btn.style.display = 'none';
                } else {
                    btn.style.display = 'flex';
                }
            });
        }

        // 初始化实时数据
        function initializeRealTimeData() {
            realTimeDataManager.subscribe(data => {
                // 这里可以更新页面上的实时数据显示
                console.log('实时数据更新:', data);
            });
        }

        // 快捷操作函数
        function openMonitor() {
            window.open('monitor.html', '_blank');
        }

        function openReports() {
            NotificationManager.info('作业报告查询功能开发中...');
        }

        function openSettings() {
            const currentRole = document.getElementById('roleSelect').value;
            const userLevel = USER_ROLES[currentRole]?.level || 0;
            
            if (userLevel < 8) {
                NotificationManager.warning('您没有权限访问系统设置');
                return;
            }
            
            NotificationManager.info('系统设置功能开发中...');
        }

        function openHelp() {
            NotificationManager.info('帮助文档功能开发中...');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        navigateToStep(1);
                        break;
                    case '2':
                        e.preventDefault();
                        navigateToStep(2);
                        break;
                    case '3':
                        e.preventDefault();
                        navigateToStep(3);
                        break;
                    case 'm':
                        e.preventDefault();
                        openMonitor();
                        break;
                }
            }
        });
    </script>
</body>
</html>
