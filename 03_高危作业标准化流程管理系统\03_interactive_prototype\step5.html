<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>步骤5：散气操作 - APP端</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        body { background: #f7f8fa; padding-bottom: 80px; }
        .mobile-container { max-width: 480px; margin: 0 auto; padding: 15px; }
        .operation-card { background: white; border-radius: 12px; padding: 20px; margin-bottom: 15px; box-shadow: 0 2px 12px rgba(0,0,0,0.08); }
        .countdown-display { text-align: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px; padding: 20px; margin-bottom: 15px; }
        .countdown-value { font-size: 32px; font-weight: bold; font-family: 'Courier New', monospace; }
        .safety-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin: 15px 0; }
        .safety-item { background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: center; border-left: 4px solid #4CAF50; }
        .safety-item.danger { border-left-color: #f44336; background: #ffebee; }
        .fixed-bottom { position: fixed; bottom: 0; left: 0; right: 0; background: white; padding: 15px; box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1000; }
        .btn-group { display: flex; gap: 10px; max-width: 480px; margin: 0 auto; }
        .btn-full { flex: 1; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div>
                    <div class="header-title">步骤5：散气操作</div>
                    <div class="header-subtitle">APP端 - 安全散气与浓度监测</div>
                </div>
                <div class="header-user">
                    <div class="user-info">
                        <div class="user-name">王五</div>
                        <div class="user-role">操作人员</div>
                    </div>
                    <div class="user-avatar">王</div>
                </div>
            </div>
        </div>
    </div>

    <div class="mobile-container">
        <!-- 熏蒸时间倒计时 -->
        <div class="countdown-display">
            <div style="font-size: 14px; opacity: 0.9; margin-bottom: 8px;">熏蒸剩余时间</div>
            <div class="countdown-value" id="countdown">24:15:30</div>
            <div style="font-size: 12px; opacity: 0.8; margin-top: 8px;">预计散气时间：2024-01-23 08:00</div>
        </div>

        <!-- 散气条件检查 -->
        <div class="operation-card">
            <div class="card-title">散气条件检查</div>
            <div class="safety-grid">
                <div class="safety-item">
                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 4px;">168小时</div>
                    <div style="font-size: 12px; color: #666;">熏蒸时间</div>
                </div>
                <div class="safety-item danger">
                    <div style="font-size: 18px; font-weight: bold; color: #c62828; margin-bottom: 4px;">850mg/m³</div>
                    <div style="font-size: 12px; color: #666;">PH3浓度</div>
                </div>
                <div class="safety-item">
                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 4px;">良好</div>
                    <div style="font-size: 12px; color: #666;">天气条件</div>
                </div>
                <div class="safety-item">
                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 4px;">正常</div>
                    <div style="font-size: 12px; color: #666;">通风设备</div>
                </div>
            </div>
        </div>

        <!-- 散气操作控制 -->
        <div class="operation-card">
            <div class="card-title">散气操作控制</div>
            <div class="form-group">
                <label class="form-label">通风方式</label>
                <select class="form-control">
                    <option>自然通风</option>
                    <option>机械通风</option>
                    <option>混合通风</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">风机功率设置</label>
                <input type="range" class="form-control" min="0" max="100" value="60" onchange="updatePower(this.value)">
                <div style="text-align: center; margin-top: 5px;">当前功率：<span id="powerValue">60</span>%</div>
            </div>
            <div style="display: flex; gap: 10px; margin-top: 15px;">
                <button class="btn btn-success" onclick="startVentilation()">开始散气</button>
                <button class="btn btn-warning" onclick="pauseVentilation()">暂停散气</button>
                <button class="btn btn-danger" onclick="stopVentilation()">停止散气</button>
            </div>
        </div>

        <!-- 安全监测 -->
        <div class="operation-card">
            <div class="card-title">安全监测</div>
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                <strong>⚠️ 安全提醒：</strong>散气过程中严禁人员进入仓房，必须在上风向监测
            </div>
            <div class="safety-grid">
                <div class="safety-item">
                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 4px;">5m/s</div>
                    <div style="font-size: 12px; color: #666;">风速</div>
                </div>
                <div class="safety-item">
                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 4px;">东南风</div>
                    <div style="font-size: 12px; color: #666;">风向</div>
                </div>
                <div class="safety-item danger">
                    <div style="font-size: 18px; font-weight: bold; color: #c62828; margin-bottom: 4px;">15mg/m³</div>
                    <div style="font-size: 12px; color: #666;">周边PH3浓度</div>
                </div>
                <div class="safety-item">
                    <div style="font-size: 18px; font-weight: bold; color: #333; margin-bottom: 4px;">安全</div>
                    <div style="font-size: 12px; color: #666;">人员状态</div>
                </div>
            </div>
        </div>

        <!-- 散气记录 -->
        <div class="operation-card">
            <div class="card-title">散气记录</div>
            <div style="background: #f8f9fa; border-radius: 8px; padding: 15px;">
                <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-size: 12px;">
                    <span style="color: #666;">15:30:00</span>
                    <span style="color: #333; font-weight: 500;">开始散气操作</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e0e0e0; font-size: 12px;">
                    <span style="color: #666;">15:45:00</span>
                    <span style="color: #333; font-weight: 500;">PH3浓度降至500mg/m³</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding: 8px 0; font-size: 12px;">
                    <span style="color: #666;">16:00:00</span>
                    <span style="color: #333; font-weight: 500;">周边浓度检测正常</span>
                </div>
            </div>
        </div>
    </div>

    <div class="fixed-bottom">
        <div class="btn-group">
            <button class="btn btn-secondary btn-full" onclick="goBack()">返回</button>
            <button class="btn btn-primary btn-full" onclick="completeVentilation()">完成散气</button>
        </div>
    </div>

    <script src="assets/common.js"></script>
    <script>
        function updatePower(value) {
            document.getElementById('powerValue').textContent = value;
        }
        
        function startVentilation() {
            NotificationManager.success('散气操作已开始');
        }
        
        function pauseVentilation() {
            NotificationManager.warning('散气操作已暂停');
        }
        
        function stopVentilation() {
            NotificationManager.info('散气操作已停止');
        }
        
        function completeVentilation() {
            if (confirm('确认完成散气操作？')) {
                workflowManager.completeStep(5, { status: 'completed' });
                NotificationManager.success('散气操作已完成！');
                setTimeout(() => window.location.href = 'step6.html', 2000);
            }
        }
        
        function goBack() {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
