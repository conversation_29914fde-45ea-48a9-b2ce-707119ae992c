-- 粮库风险监测系统 - 处置时限计算功能数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS grain_risk_monitor DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE grain_risk_monitor;

-- 1. 节假日配置表
CREATE TABLE holiday_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    holiday_date DATE NOT NULL COMMENT '节假日日期',
    holiday_name VARCHAR(100) NOT NULL COMMENT '节假日名称',
    holiday_type TINYINT NOT NULL DEFAULT 1 COMMENT '节假日类型：1-法定节假日，2-调休，3-自定义休息日',
    is_workday TINYINT NOT NULL DEFAULT 0 COMMENT '是否为工作日：0-休息日，1-工作日（用于调休）',
    year_num INT NOT NULL COMMENT '年份',
    remark VARCHAR(500) COMMENT '备注说明',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',

    UNIQUE KEY uk_holiday_date (holiday_date),
    KEY idx_year_type (year_num, holiday_type),
    KEY idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='节假日配置表';

-- 2. 风险类型配置表
CREATE TABLE risk_type_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    risk_type_code VARCHAR(50) NOT NULL COMMENT '风险类型编码',
    risk_type_name VARCHAR(100) NOT NULL COMMENT '风险类型名称',
    default_dispose_days INT NOT NULL DEFAULT 3 COMMENT '默认处置时限（工作日）',
    risk_level TINYINT NOT NULL DEFAULT 1 COMMENT '风险等级：1-低风险，2-中风险，3-高风险',
    description VARCHAR(500) COMMENT '风险描述',
    is_active TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',

    UNIQUE KEY uk_risk_type_code (risk_type_code),
    KEY idx_risk_level (risk_level),
    KEY idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风险类型配置表';

-- 3. 风险事件记录表
CREATE TABLE risk_event (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    event_no VARCHAR(50) NOT NULL COMMENT '事件编号',
    grain_depot_code VARCHAR(50) NOT NULL COMMENT '粮库编码',
    grain_depot_name VARCHAR(100) NOT NULL COMMENT '粮库名称',
    risk_type_code VARCHAR(50) NOT NULL COMMENT '风险类型编码',
    risk_type_name VARCHAR(100) NOT NULL COMMENT '风险类型名称',
    risk_description TEXT COMMENT '风险描述',
    risk_level TINYINT NOT NULL DEFAULT 1 COMMENT '风险等级：1-低风险，2-中风险，3-高风险',
    occur_time DATETIME NOT NULL COMMENT '风险发生时间',
    dispose_days INT NOT NULL COMMENT '处置时限（工作日）',
    dispose_deadline DATETIME NOT NULL COMMENT '处置截止时间',
    actual_dispose_time DATETIME COMMENT '实际处置时间',
    dispose_status TINYINT NOT NULL DEFAULT 1 COMMENT '处置状态：1-待处置，2-处置中，3-已处置，4-超时未处置',
    is_overtime TINYINT NOT NULL DEFAULT 0 COMMENT '是否超时：0-未超时，1-已超时',
    overtime_hours DECIMAL(10,2) DEFAULT 0 COMMENT '超时小时数',
    dispose_result TEXT COMMENT '处置结果',
    dispose_person VARCHAR(50) COMMENT '处置人',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    is_deleted TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',

    UNIQUE KEY uk_event_no (event_no),
    KEY idx_grain_depot (grain_depot_code),
    KEY idx_risk_type (risk_type_code),
    KEY idx_occur_time (occur_time),
    KEY idx_dispose_deadline (dispose_deadline),
    KEY idx_dispose_status (dispose_status),
    KEY idx_is_overtime (is_overtime),
    KEY idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风险事件记录表';

-- 4. 处置时限计算日志表
CREATE TABLE dispose_time_calc_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    event_id BIGINT NOT NULL COMMENT '风险事件ID',
    event_no VARCHAR(50) NOT NULL COMMENT '事件编号',
    occur_time DATETIME NOT NULL COMMENT '风险发生时间',
    dispose_days INT NOT NULL COMMENT '处置时限（工作日）',
    calc_start_date DATE NOT NULL COMMENT '计算开始日期（下一个工作日）',
    calc_end_date DATE NOT NULL COMMENT '计算结束日期',
    dispose_deadline DATETIME NOT NULL COMMENT '处置截止时间',
    excluded_dates JSON COMMENT '排除的日期（节假日、周末）',
    calc_detail JSON COMMENT '计算详情',
    calc_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',

    KEY idx_event_id (event_id),
    KEY idx_event_no (event_no),
    KEY idx_calc_time (calc_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='处置时限计算日志表';

-- 5. 系统配置表
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_desc VARCHAR(200) COMMENT '配置描述',
    config_type VARCHAR(50) NOT NULL DEFAULT 'STRING' COMMENT '配置类型：STRING,INTEGER,BOOLEAN,JSON',
    is_active TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',

    UNIQUE KEY uk_config_key (config_key),
    KEY idx_config_type (config_type),
    KEY idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';