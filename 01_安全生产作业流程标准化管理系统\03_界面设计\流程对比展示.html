<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流程节点灵活性展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .flow-column {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #007bff;
        }
        
        .flow-column.simple {
            border-left-color: #28a745;
        }
        
        .flow-column.standard {
            border-left-color: #007bff;
        }
        
        .flow-column.complex {
            border-left-color: #dc3545;
        }
        
        .flow-column.custom {
            border-left-color: #ffc107;
        }
        
        .flow-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .flow-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .flow-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .badge-simple {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-standard {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .badge-complex {
            background: #f8d7da;
            color: #721c24;
        }
        
        .badge-custom {
            background: #fff3cd;
            color: #856404;
        }
        
        .flow-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 11px;
            color: #6c757d;
            margin-top: 2px;
        }
        
        .flow-nodes {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .flow-node {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }
        
        .flow-node:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .flow-node.required {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        
        .flow-node.optional {
            border-left-color: #6c757d;
        }
        
        .flow-node.new {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        
        .node-number {
            background: #007bff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }
        
        .node-number.required {
            background: #28a745;
        }
        
        .node-number.optional {
            background: #6c757d;
        }
        
        .node-number.new {
            background: #ffc107;
            color: #212529;
        }
        
        .node-icon {
            font-size: 16px;
            flex-shrink: 0;
        }
        
        .node-name {
            flex: 1;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .node-badge {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .badge-required {
            background: #d4edda;
            color: #155724;
        }
        
        .badge-optional {
            background: #e2e3e5;
            color: #383d41;
        }
        
        .badge-new {
            background: #fff3cd;
            color: #856404;
        }
        
        .flexibility-demo {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 12px;
            padding: 30px;
            margin-top: 30px;
        }
        
        .flexibility-demo h2 {
            color: #1976d2;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .demo-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .demo-btn {
            background: white;
            border: 2px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #1976d2;
            font-weight: bold;
        }
        
        .demo-btn:hover {
            background: #2196f3;
            color: white;
            transform: translateY(-2px);
        }
        
        .demo-result {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #2196f3;
            display: none;
        }
        
        .demo-result.active {
            display: block;
        }
        
        .back-button {
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔀 流程节点灵活性展示</h1>
            <p>展示不同复杂度的流程配置，体现节点增减和顺序调整的灵活性</p>
        </div>
        
        <div class="main-content">
            <div class="back-button">
                <a href="差异化配置.html" class="btn btn-secondary">← 返回配置</a>
            </div>
            
            <!-- 流程对比 -->
            <div class="comparison-grid">
                <!-- 简化版流程 -->
                <div class="flow-column simple">
                    <div class="flow-header">
                        <div class="flow-title">🏪 简化版流程</div>
                        <span class="flow-badge badge-simple">4个节点</span>
                    </div>
                    
                    <div class="flow-stats">
                        <div class="stat-item">
                            <div class="stat-value">4</div>
                            <div class="stat-label">节点数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">0.5-1h</div>
                            <div class="stat-label">处理时间</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">小型库点</div>
                            <div class="stat-label">适用场景</div>
                        </div>
                    </div>
                    
                    <div class="flow-nodes">
                        <div class="flow-node required">
                            <span class="node-number required">1</span>
                            <span class="node-icon">🟢</span>
                            <span class="node-name">申请提交</span>
                            <span class="node-badge badge-required">必需</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">2</span>
                            <span class="node-icon">✅</span>
                            <span class="node-name">安全确认</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">3</span>
                            <span class="node-icon">⚙️</span>
                            <span class="node-name">开始作业</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node required">
                            <span class="node-number required">4</span>
                            <span class="node-icon">🔴</span>
                            <span class="node-name">完工记录</span>
                            <span class="node-badge badge-required">必需</span>
                        </div>
                    </div>
                </div>
                
                <!-- 标准版流程 -->
                <div class="flow-column standard">
                    <div class="flow-header">
                        <div class="flow-title">🏢 标准版流程</div>
                        <span class="flow-badge badge-standard">5个节点</span>
                    </div>
                    
                    <div class="flow-stats">
                        <div class="stat-item">
                            <div class="stat-value">5</div>
                            <div class="stat-label">节点数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">2-4h</div>
                            <div class="stat-label">处理时间</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">中型库点</div>
                            <div class="stat-label">适用场景</div>
                        </div>
                    </div>
                    
                    <div class="flow-nodes">
                        <div class="flow-node required">
                            <span class="node-number required">1</span>
                            <span class="node-icon">🟢</span>
                            <span class="node-name">申请提交</span>
                            <span class="node-badge badge-required">必需</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">2</span>
                            <span class="node-icon">✅</span>
                            <span class="node-name">部门审批</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">3</span>
                            <span class="node-icon">🔍</span>
                            <span class="node-name">安全检查</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">4</span>
                            <span class="node-icon">⚙️</span>
                            <span class="node-name">开始作业</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node required">
                            <span class="node-number required">5</span>
                            <span class="node-icon">🔴</span>
                            <span class="node-name">作业验收</span>
                            <span class="node-badge badge-required">必需</span>
                        </div>
                    </div>
                </div>
                
                <!-- 严格版流程 -->
                <div class="flow-column complex">
                    <div class="flow-header">
                        <div class="flow-title">🏭 严格版流程</div>
                        <span class="flow-badge badge-complex">9个节点</span>
                    </div>
                    
                    <div class="flow-stats">
                        <div class="stat-item">
                            <div class="stat-value">9</div>
                            <div class="stat-label">节点数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">3-5h</div>
                            <div class="stat-label">处理时间</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">大型库点</div>
                            <div class="stat-label">适用场景</div>
                        </div>
                    </div>
                    
                    <div class="flow-nodes">
                        <div class="flow-node required">
                            <span class="node-number required">1</span>
                            <span class="node-icon">🟢</span>
                            <span class="node-name">申请提交</span>
                            <span class="node-badge badge-required">必需</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">2</span>
                            <span class="node-icon">📋</span>
                            <span class="node-name">初步审核</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">3</span>
                            <span class="node-icon">✅</span>
                            <span class="node-name">安全主管审批</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">4</span>
                            <span class="node-icon">👔</span>
                            <span class="node-name">总经理审批</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">5</span>
                            <span class="node-icon">🌱</span>
                            <span class="node-name">环保审批</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">6</span>
                            <span class="node-icon">🔍</span>
                            <span class="node-name">现场检查</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">7</span>
                            <span class="node-icon">🧪</span>
                            <span class="node-name">气体检测</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">8</span>
                            <span class="node-icon">⚙️</span>
                            <span class="node-name">开始作业</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node required">
                            <span class="node-number required">9</span>
                            <span class="node-icon">🔴</span>
                            <span class="node-name">作业验收</span>
                            <span class="node-badge badge-required">必需</span>
                        </div>
                    </div>
                </div>
                
                <!-- 自定义流程 -->
                <div class="flow-column custom">
                    <div class="flow-header">
                        <div class="flow-title">🎨 朝阳库点定制版</div>
                        <span class="flow-badge badge-custom">7个节点</span>
                    </div>
                    
                    <div class="flow-stats">
                        <div class="stat-item">
                            <div class="stat-value">7</div>
                            <div class="stat-label">节点数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">1-2h</div>
                            <div class="stat-label">处理时间</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">定制需求</div>
                            <div class="stat-label">适用场景</div>
                        </div>
                    </div>
                    
                    <div class="flow-nodes">
                        <div class="flow-node required">
                            <span class="node-number required">1</span>
                            <span class="node-icon">🟢</span>
                            <span class="node-name">申请提交</span>
                            <span class="node-badge badge-required">必需</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">2</span>
                            <span class="node-icon">✅</span>
                            <span class="node-name">部门审批</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node new">
                            <span class="node-number new">3</span>
                            <span class="node-icon">🌱</span>
                            <span class="node-name">环保审批</span>
                            <span class="node-badge badge-new">新增</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">4</span>
                            <span class="node-icon">🔍</span>
                            <span class="node-name">安全检查</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node new">
                            <span class="node-number new">5</span>
                            <span class="node-icon">📷</span>
                            <span class="node-name">现场拍照</span>
                            <span class="node-badge badge-new">新增</span>
                        </div>
                        
                        <div class="flow-node optional">
                            <span class="node-number optional">6</span>
                            <span class="node-icon">⚙️</span>
                            <span class="node-name">开始作业</span>
                            <span class="node-badge badge-optional">可选</span>
                        </div>
                        
                        <div class="flow-node required">
                            <span class="node-number required">7</span>
                            <span class="node-icon">🔴</span>
                            <span class="node-name">作业验收</span>
                            <span class="node-badge badge-required">必需</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 灵活性演示 -->
            <div class="flexibility-demo">
                <h2>🎯 流程节点灵活性演示</h2>

                <div class="demo-actions">
                    <div class="demo-btn" onclick="showDemo('add')">
                        ➕ 增加节点
                    </div>
                    <div class="demo-btn" onclick="showDemo('remove')">
                        ➖ 删除节点
                    </div>
                    <div class="demo-btn" onclick="showDemo('reorder')">
                        🔄 调整顺序
                    </div>
                    <div class="demo-btn" onclick="showDemo('modify')">
                        ✏️ 修改属性
                    </div>
                </div>

                <div id="demo-add" class="demo-result">
                    <h4>➕ 增加节点演示</h4>
                    <p><strong>场景：</strong>朝阳库点需要增加"气体检测"节点</p>
                    <p><strong>操作：</strong>从节点库拖拽"气体检测"节点到"开始作业"前</p>
                    <p><strong>结果：</strong>流程变为 7个节点 → 8个节点，增加安全保障</p>
                    <div style="background: #f8fff9; padding: 10px; border-radius: 4px; margin-top: 10px; border-left: 3px solid #28a745;">
                        <strong>✅ 优势：</strong>无需重新开发，拖拽即可完成配置
                    </div>
                </div>

                <div id="demo-remove" class="demo-result">
                    <h4>➖ 删除节点演示</h4>
                    <p><strong>场景：</strong>小型库点认为"环保审批"节点不必要</p>
                    <p><strong>操作：</strong>点击节点上的删除按钮（×）</p>
                    <p><strong>结果：</strong>流程变为 7个节点 → 6个节点，提高处理效率</p>
                    <div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin-top: 10px; border-left: 3px solid #ffc107;">
                        <strong>⚠️ 注意：</strong>必需节点（绿色标记）不能删除，确保安全底线
                    </div>
                </div>

                <div id="demo-reorder" class="demo-result">
                    <h4>🔄 调整顺序演示</h4>
                    <p><strong>场景：</strong>希望在安全检查前先进行现场拍照</p>
                    <p><strong>操作：</strong>点击"现场拍照"节点的上移按钮（↑）</p>
                    <p><strong>结果：</strong>"现场拍照"移动到"安全检查"前，优化流程顺序</p>
                    <div style="background: #e3f2fd; padding: 10px; border-radius: 4px; margin-top: 10px; border-left: 3px solid #2196f3;">
                        <strong>💡 灵活：</strong>支持任意调整节点顺序，适应不同作业习惯
                    </div>
                </div>

                <div id="demo-modify" class="demo-result">
                    <h4>✏️ 修改属性演示</h4>
                    <p><strong>场景：</strong>调整"部门审批"的处理时限</p>
                    <p><strong>操作：</strong>点击节点 → 编辑属性 → 修改时限从2小时改为1小时</p>
                    <p><strong>结果：</strong>审批时限缩短，提高整体处理效率</p>
                    <div style="background: #f8d7da; padding: 10px; border-radius: 4px; margin-top: 10px; border-left: 3px solid #dc3545;">
                        <strong>🎯 精准：</strong>每个节点的属性都可以独立配置和调整
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showDemo(type) {
            // 隐藏所有演示结果
            document.querySelectorAll('.demo-result').forEach(result => {
                result.classList.remove('active');
            });

            // 显示选中的演示结果
            document.getElementById('demo-' + type).classList.add('active');
        }
    </script>
</body>
</html>
