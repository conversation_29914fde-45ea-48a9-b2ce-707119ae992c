<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节假日管理 - 粮库风险监测系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 日期选择器 -->
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="css/holiday-management.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-seedling me-2"></i>
                粮库风险监测系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#"><i class="fas fa-user me-1"></i>管理员</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>功能菜单</h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="#" class="list-group-item list-group-item-action active">
                            <i class="fas fa-calendar-alt me-2"></i>节假日管理
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-calculator me-2"></i>时限计算
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-exclamation-triangle me-2"></i>风险监测
                        </a>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-calendar-alt me-2"></i>节假日配置管理</h2>
                    <div>
                        <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
                            <i class="fas fa-plus me-1"></i>新增节假日
                        </button>
                        <button type="button" class="btn btn-info me-2" onclick="importHolidays()">
                            <i class="fas fa-upload me-1"></i>批量导入
                        </button>
                        <button type="button" class="btn btn-warning" onclick="exportHolidays()">
                            <i class="fas fa-download me-1"></i>导出数据
                        </button>
                    </div>
                </div>

                <!-- 搜索筛选区 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form class="row g-3" id="searchForm">
                            <div class="col-md-3">
                                <label for="yearSelect" class="form-label">年份</label>
                                <select class="form-select" id="yearSelect">
                                    <option value="">全部年份</option>
                                    <option value="2024">2024年</option>
                                    <option value="2025">2025年</option>
                                    <option value="2026">2026年</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="holidayTypeSelect" class="form-label">节假日类型</label>
                                <select class="form-select" id="holidayTypeSelect">
                                    <option value="">全部类型</option>
                                    <option value="1">法定节假日</option>
                                    <option value="2">调休</option>
                                    <option value="3">自定义休息日</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="holidayName" class="form-label">节假日名称</label>
                                <input type="text" class="form-control" id="holidayName" placeholder="请输入节假日名称">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="searchHolidays()">
                                        <i class="fas fa-search me-1"></i>搜索
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">节假日列表</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="holidayTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>序号</th>
                                        <th>日期</th>
                                        <th>节假日名称</th>
                                        <th>类型</th>
                                        <th>是否工作日</th>
                                        <th>年份</th>
                                        <th>备注</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="holidayTableBody">
                                    <!-- 数据将通过JavaScript动态加载 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="分页导航" class="mt-3">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- 分页按钮将通过JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑节假日模态框 -->
    <div class="modal fade" id="addHolidayModal" tabindex="-1" aria-labelledby="addHolidayModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addHolidayModalLabel">
                        <i class="fas fa-plus me-2"></i>新增节假日
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="holidayForm">
                        <input type="hidden" id="holidayId" name="id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalHolidayDate" class="form-label">节假日日期 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="modalHolidayDate" name="holidayDate" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalHolidayName" class="form-label">节假日名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="modalHolidayName" name="holidayName" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalHolidayType" class="form-label">节假日类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="modalHolidayType" name="holidayType" required>
                                        <option value="">请选择类型</option>
                                        <option value="1">法定节假日</option>
                                        <option value="2">调休</option>
                                        <option value="3">自定义休息日</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalIsWorkday" class="form-label">是否工作日</label>
                                    <select class="form-select" id="modalIsWorkday" name="isWorkday">
                                        <option value="0">休息日</option>
                                        <option value="1">工作日</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalYearNum" class="form-label">年份 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="modalYearNum" name="yearNum" required>
                                        <option value="">请选择年份</option>
                                        <option value="2024">2024年</option>
                                        <option value="2025">2025年</option>
                                        <option value="2026">2026年</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="modalRemark" class="form-label">备注</label>
                                    <input type="text" class="form-control" id="modalRemark" name="remark">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveHoliday()">
                        <i class="fas fa-save me-1"></i>保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">
                        <i class="fas fa-upload me-2"></i>批量导入节假日
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls">
                        <div class="form-text">支持.xlsx和.xls格式，请按照模板格式填写数据</div>
                    </div>
                    <div class="mb-3">
                        <a href="#" class="btn btn-outline-info btn-sm" onclick="downloadTemplate()">
                            <i class="fas fa-download me-1"></i>下载导入模板
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="uploadFile()">
                        <i class="fas fa-upload me-1"></i>开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/holiday-management.js"></script>
</body>
</html>