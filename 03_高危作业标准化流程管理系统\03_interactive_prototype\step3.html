<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>步骤3：施药操作 - APP端</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        body {
            background: #f7f8fa;
            padding-bottom: 80px;
        }
        
        .mobile-container {
            max-width: 480px;
            margin: 0 auto;
            padding: 15px;
        }
        
        .operation-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .timer-display {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .timer-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }
        
        .timer-value {
            font-size: 32px;
            font-weight: bold;
            font-family: 'Courier New', monospace;
        }
        
        .timer-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }
        
        .timer-btn {
            padding: 8px 16px;
            border: 1px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .timer-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .dosage-input {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 15px;
            align-items: center;
            margin: 20px 0;
        }
        
        .dosage-item {
            text-align: center;
        }
        
        .dosage-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .dosage-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background: white;
        }
        
        .dosage-value.editable {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .dosage-arrow {
            font-size: 20px;
            color: #4CAF50;
        }
        
        .location-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
        }
        
        .location-item {
            aspect-ratio: 1;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            background: white;
        }
        
        .location-item:hover {
            border-color: #4CAF50;
        }
        
        .location-item.selected {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .location-item.completed {
            border-color: #2196F3;
            background: #e3f2fd;
        }
        
        .location-number {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .location-status {
            font-size: 10px;
            color: #666;
        }
        
        .monitor-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 15px 0;
        }
        
        .monitor-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #4CAF50;
        }
        
        .monitor-item.warning {
            border-left-color: #ff9800;
            background: #fff8e1;
        }
        
        .monitor-item.danger {
            border-left-color: #f44336;
            background: #ffebee;
        }
        
        .monitor-value {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }
        
        .monitor-item.warning .monitor-value {
            color: #ef6c00;
        }
        
        .monitor-item.danger .monitor-value {
            color: #c62828;
        }
        
        .monitor-label {
            font-size: 12px;
            color: #666;
        }
        
        .safety-alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .alert-title {
            font-size: 14px;
            font-weight: bold;
            color: #856404;
            margin-bottom: 8px;
        }
        
        .alert-content {
            font-size: 12px;
            color: #856404;
            line-height: 1.4;
        }
        
        .photo-capture {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px 0;
        }
        
        .capture-item {
            aspect-ratio: 4/3;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s;
            background: #fafafa;
        }
        
        .capture-item:hover {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .capture-item.has-photo {
            border-style: solid;
            border-color: #4CAF50;
            background-size: cover;
            background-position: center;
        }
        
        .capture-icon {
            font-size: 24px;
            color: #999;
            margin-bottom: 8px;
        }
        
        .capture-text {
            font-size: 12px;
            color: #666;
            text-align: center;
        }
        
        .operation-log {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
            font-size: 12px;
        }
        
        .log-item:last-child {
            border-bottom: none;
        }
        
        .log-time {
            color: #666;
        }
        
        .log-action {
            color: #333;
            font-weight: 500;
        }
        
        .fixed-bottom {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            max-width: 480px;
            margin: 0 auto;
        }
        
        .btn-full {
            flex: 1;
        }
        
        .emergency-btn {
            position: fixed;
            top: 50%;
            right: 15px;
            transform: translateY(-50%);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #f44336;
            color: white;
            border: none;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 4px 16px rgba(244, 67, 54, 0.4);
            z-index: 999;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div>
                    <div class="header-title">步骤3：施药操作</div>
                    <div class="header-subtitle">APP端 - 实时操作记录与监控</div>
                </div>
                <div class="header-user">
                    <div class="user-info">
                        <div class="user-name">王五</div>
                        <div class="user-role">操作人员</div>
                    </div>
                    <div class="user-avatar">王</div>
                </div>
            </div>
        </div>
    </div>

    <div class="mobile-container">
        <!-- 操作计时器 -->
        <div class="timer-display">
            <div class="timer-label">施药操作进行时间</div>
            <div class="timer-value" id="operationTimer">00:00:00</div>
            <div class="timer-controls">
                <button class="timer-btn" id="startBtn" onclick="startOperation()">开始</button>
                <button class="timer-btn" id="pauseBtn" onclick="pauseOperation()" disabled>暂停</button>
                <button class="timer-btn" id="stopBtn" onclick="stopOperation()" disabled>结束</button>
            </div>
        </div>

        <!-- 用药量记录 -->
        <div class="operation-card">
            <div class="card-title">用药量记录</div>
            <div class="dosage-input">
                <div class="dosage-item">
                    <div class="dosage-label">计划用量</div>
                    <div class="dosage-value">15.5 kg</div>
                </div>
                <div class="dosage-arrow">→</div>
                <div class="dosage-item">
                    <div class="dosage-label">实际用量</div>
                    <input type="number" class="dosage-value editable" id="actualDosage" 
                           placeholder="0.0" step="0.1" onchange="updateDosage()">
                </div>
            </div>
            <div style="text-align: center; font-size: 12px; color: #666;">
                用药差异：<span id="dosageDiff">待输入</span>
            </div>
        </div>

        <!-- 施药位置选择 -->
        <div class="operation-card">
            <div class="card-title">施药位置标记</div>
            <div class="location-grid">
                <div class="location-item" onclick="selectLocation(1)">
                    <div class="location-number">1</div>
                    <div class="location-status">待施药</div>
                </div>
                <div class="location-item" onclick="selectLocation(2)">
                    <div class="location-number">2</div>
                    <div class="location-status">待施药</div>
                </div>
                <div class="location-item" onclick="selectLocation(3)">
                    <div class="location-number">3</div>
                    <div class="location-status">待施药</div>
                </div>
                <div class="location-item" onclick="selectLocation(4)">
                    <div class="location-number">4</div>
                    <div class="location-status">待施药</div>
                </div>
                <div class="location-item" onclick="selectLocation(5)">
                    <div class="location-number">5</div>
                    <div class="location-status">待施药</div>
                </div>
                <div class="location-item" onclick="selectLocation(6)">
                    <div class="location-number">6</div>
                    <div class="location-status">待施药</div>
                </div>
            </div>
            <div style="font-size: 12px; color: #666; margin-top: 10px;">
                点击位置标记完成施药，已完成：<span id="completedLocations">0</span>/6
            </div>
        </div>

        <!-- 实时监测数据 -->
        <div class="operation-card">
            <div class="card-title">实时监测数据</div>
            <div class="monitor-grid">
                <div class="monitor-item">
                    <div class="monitor-value" id="temperature">23.5°C</div>
                    <div class="monitor-label">仓内温度</div>
                </div>
                <div class="monitor-item">
                    <div class="monitor-value" id="humidity">65%</div>
                    <div class="monitor-label">相对湿度</div>
                </div>
                <div class="monitor-item warning">
                    <div class="monitor-value" id="ph3">850</div>
                    <div class="monitor-label">PH3浓度(mg/m³)</div>
                </div>
                <div class="monitor-item">
                    <div class="monitor-value" id="o2">18.5%</div>
                    <div class="monitor-label">氧气浓度</div>
                </div>
            </div>
            <div style="text-align: center; font-size: 12px; color: #666; margin-top: 10px;">
                最后更新：<span id="updateTime">2024-01-16 14:30:25</span>
            </div>
        </div>

        <!-- 安全提醒 -->
        <div class="safety-alert">
            <div class="alert-title">⚠️ 安全提醒</div>
            <div class="alert-content">
                1. 施药过程中必须佩戴防毒面具<br>
                2. 严禁在施药区域吸烟或使用明火<br>
                3. 如出现头晕、恶心等症状立即撤离<br>
                4. 施药完成后立即清点人员
            </div>
        </div>

        <!-- 现场拍照 -->
        <div class="operation-card">
            <div class="card-title">现场拍照记录</div>
            <div class="photo-capture">
                <div class="capture-item" onclick="takePhoto('before')">
                    <div class="capture-icon">📷</div>
                    <div class="capture-text">施药前照片</div>
                </div>
                <div class="capture-item" onclick="takePhoto('during')">
                    <div class="capture-icon">📷</div>
                    <div class="capture-text">施药过程照片</div>
                </div>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="operation-card">
            <div class="card-title">操作日志</div>
            <div class="operation-log" id="operationLog">
                <div class="log-item">
                    <span class="log-time">14:30:25</span>
                    <span class="log-action">开始施药操作</span>
                </div>
                <div class="log-item">
                    <span class="log-time">14:31:10</span>
                    <span class="log-action">位置1施药完成</span>
                </div>
                <div class="log-item">
                    <span class="log-time">14:32:45</span>
                    <span class="log-action">位置2施药完成</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 紧急停止按钮 -->
    <button class="emergency-btn" onclick="emergencyStop()">
        紧急<br>停止
    </button>

    <!-- 底部操作按钮 -->
    <div class="fixed-bottom">
        <div class="btn-group">
            <button class="btn btn-secondary btn-full" onclick="goBack()">返回</button>
            <button class="btn btn-primary btn-full" id="completeBtn" onclick="completeOperation()" disabled>完成施药</button>
        </div>
    </div>

    <script src="assets/common.js"></script>
    <script>
        let operationStartTime = null;
        let operationTimer = null;
        let isOperationRunning = false;
        let completedLocations = 0;
        let actualDosageValue = 0;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeRealTimeData();
            updateCompleteButton();
        });

        // 开始操作
        function startOperation() {
            operationStartTime = new Date();
            isOperationRunning = true;
            
            document.getElementById('startBtn').disabled = true;
            document.getElementById('pauseBtn').disabled = false;
            document.getElementById('stopBtn').disabled = false;
            
            // 启动计时器
            operationTimer = setInterval(updateTimer, 1000);
            
            // 记录日志
            addOperationLog('开始施药操作');
            
            NotificationManager.success('施药操作已开始');
        }

        // 暂停操作
        function pauseOperation() {
            isOperationRunning = false;
            clearInterval(operationTimer);
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
            
            addOperationLog('暂停施药操作');
            NotificationManager.warning('施药操作已暂停');
        }

        // 停止操作
        function stopOperation() {
            isOperationRunning = false;
            clearInterval(operationTimer);
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('pauseBtn').disabled = true;
            document.getElementById('stopBtn').disabled = true;
            
            addOperationLog('结束施药操作');
            NotificationManager.info('施药操作已结束');
        }

        // 更新计时器
        function updateTimer() {
            if (!operationStartTime) return;
            
            const now = new Date();
            const elapsed = Math.floor((now - operationStartTime) / 1000);
            
            const hours = Math.floor(elapsed / 3600);
            const minutes = Math.floor((elapsed % 3600) / 60);
            const seconds = elapsed % 60;
            
            const timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            document.getElementById('operationTimer').textContent = timeString;
        }

        // 选择施药位置
        function selectLocation(locationId) {
            const locationItem = event.currentTarget;
            
            if (locationItem.classList.contains('completed')) {
                NotificationManager.info(`位置${locationId}已完成施药`);
                return;
            }
            
            if (!isOperationRunning) {
                NotificationManager.warning('请先开始施药操作');
                return;
            }
            
            // 标记为已完成
            locationItem.classList.add('completed');
            locationItem.querySelector('.location-status').textContent = '已完成';
            
            completedLocations++;
            document.getElementById('completedLocations').textContent = completedLocations;
            
            // 记录日志
            addOperationLog(`位置${locationId}施药完成`);
            
            // 更新完成按钮状态
            updateCompleteButton();
            
            NotificationManager.success(`位置${locationId}施药完成`);
        }

        // 更新用药量
        function updateDosage() {
            const actualInput = document.getElementById('actualDosage');
            actualDosageValue = parseFloat(actualInput.value) || 0;
            const plannedDosage = 15.5;
            
            const diff = actualDosageValue - plannedDosage;
            const diffText = diff > 0 ? `+${diff.toFixed(1)}kg` : `${diff.toFixed(1)}kg`;
            const diffColor = Math.abs(diff) > 1 ? '#f44336' : '#4CAF50';
            
            const diffElement = document.getElementById('dosageDiff');
            diffElement.textContent = diffText;
            diffElement.style.color = diffColor;
            
            updateCompleteButton();
        }

        // 更新完成按钮状态
        function updateCompleteButton() {
            const completeBtn = document.getElementById('completeBtn');
            const canComplete = completedLocations === 6 && actualDosageValue > 0 && !isOperationRunning;
            
            completeBtn.disabled = !canComplete;
            if (canComplete) {
                completeBtn.classList.remove('btn-secondary');
                completeBtn.classList.add('btn-success');
            } else {
                completeBtn.classList.remove('btn-success');
                completeBtn.classList.add('btn-secondary');
            }
        }

        // 拍照功能
        function takePhoto(type) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.capture = 'camera';
            input.click();
            
            input.addEventListener('change', function() {
                if (input.files.length > 0) {
                    const file = input.files[0];
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        // 更新对应的照片区域
                        const captureItems = document.querySelectorAll('.capture-item');
                        captureItems.forEach(item => {
                            if (item.onclick.toString().includes(type)) {
                                item.style.backgroundImage = `url(${e.target.result})`;
                                item.classList.add('has-photo');
                                item.innerHTML = '<div style="background: rgba(0,0,0,0.7); color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">已拍摄</div>';
                            }
                        });
                    };
                    
                    reader.readAsDataURL(file);
                    
                    const photoName = type === 'before' ? '施药前' : '施药过程';
                    addOperationLog(`${photoName}照片已保存`);
                    NotificationManager.success(`${photoName}照片已保存`);
                }
            });
        }

        // 添加操作日志
        function addOperationLog(action) {
            const logContainer = document.getElementById('operationLog');
            const now = new Date();
            const timeString = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
            
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `
                <span class="log-time">${timeString}</span>
                <span class="log-action">${action}</span>
            `;
            
            logContainer.insertBefore(logItem, logContainer.firstChild);
            
            // 限制日志条数
            const logItems = logContainer.querySelectorAll('.log-item');
            if (logItems.length > 10) {
                logContainer.removeChild(logItems[logItems.length - 1]);
            }
        }

        // 初始化实时数据
        function initializeRealTimeData() {
            realTimeDataManager.subscribe(data => {
                document.getElementById('temperature').textContent = Utils.formatNumber(data.temperature, 1) + '°C';
                document.getElementById('humidity').textContent = Math.round(data.humidity) + '%';
                document.getElementById('ph3').textContent = Math.round(data.ph3Concentration);
                document.getElementById('o2').textContent = Utils.formatNumber(data.o2Concentration, 1) + '%';
                document.getElementById('updateTime').textContent = Utils.formatDateTime(data.lastUpdate);
                
                // 检查异常情况
                const ph3Element = document.getElementById('ph3').parentElement;
                if (data.ph3Concentration > 900) {
                    ph3Element.className = 'monitor-item danger';
                } else if (data.ph3Concentration > 850) {
                    ph3Element.className = 'monitor-item warning';
                } else {
                    ph3Element.className = 'monitor-item';
                }
            });
        }

        // 紧急停止
        function emergencyStop() {
            if (confirm('确认紧急停止施药操作？')) {
                stopOperation();
                addOperationLog('紧急停止施药操作');
                NotificationManager.error('施药操作已紧急停止！');
                
                // 可以添加紧急处理逻辑
                setTimeout(() => {
                    alert('请立即撤离现场并联系安全管理人员！');
                }, 1000);
            }
        }

        // 完成施药操作
        function completeOperation() {
            if (completedLocations !== 6) {
                NotificationManager.error('请完成所有位置的施药操作');
                return;
            }
            
            if (actualDosageValue <= 0) {
                NotificationManager.error('请输入实际用药量');
                return;
            }
            
            if (isOperationRunning) {
                NotificationManager.error('请先结束施药操作');
                return;
            }
            
            // 保存施药操作数据
            const operationData = {
                actualDosage: actualDosageValue,
                completedLocations: completedLocations,
                operationDuration: document.getElementById('operationTimer').textContent,
                completedAt: new Date().toISOString(),
                status: 'completed'
            };
            
            workflowManager.completeStep(3, operationData);
            
            NotificationManager.success('施药操作已完成！');
            
            setTimeout(() => {
                window.location.href = 'step4.html';
            }, 2000);
        }

        // 返回
        function goBack() {
            if (isOperationRunning) {
                if (confirm('施药操作正在进行中，确认返回？')) {
                    stopOperation();
                    window.location.href = 'index.html';
                }
            } else {
                window.location.href = 'index.html';
            }
        }

        // 防止页面缩放
        document.addEventListener('touchstart', function(event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        });

        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
