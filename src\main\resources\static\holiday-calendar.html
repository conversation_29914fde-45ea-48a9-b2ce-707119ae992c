<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日历视图节假日管理 - 粮库风险监测系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义样式 -->
    <link href="css/holiday-calendar.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-seedling me-2"></i>
                粮库风险监测系统
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="holiday-management.html">
                    <i class="fas fa-list me-1"></i>列表视图
                </a>
                <a class="nav-link" href="#">
                    <i class="fas fa-user me-1"></i>管理员
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2">
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-list me-2"></i>功能菜单</h6>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="holiday-management.html" class="list-group-item list-group-item-action">
                            <i class="fas fa-table me-2"></i>列表管理
                        </a>
                        <a href="#" class="list-group-item list-group-item-action active">
                            <i class="fas fa-calendar me-2"></i>日历视图
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-calculator me-2"></i>时限计算
                        </a>
                        <a href="#" class="list-group-item list-group-item-action">
                            <i class="fas fa-exclamation-triangle me-2"></i>风险监测
                        </a>
                    </div>
                </div>

                <!-- 图例说明 -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>图例说明</h6>
                    </div>
                    <div class="card-body">
                        <div class="legend-item mb-2">
                            <span class="legend-color weekend"></span>
                            <span class="legend-text">双休日</span>
                        </div>
                        <div class="legend-item mb-2">
                            <span class="legend-color legal-holiday"></span>
                            <span class="legend-text">法定节假日</span>
                        </div>
                        <div class="legend-item mb-2">
                            <span class="legend-color adjust-workday"></span>
                            <span class="legend-text">调休工作日</span>
                        </div>
                        <div class="legend-item mb-2">
                            <span class="legend-color custom-holiday"></span>
                            <span class="legend-text">自定义休息日</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color today"></span>
                            <span class="legend-text">今天</span>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-tools me-2"></i>快速操作</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success btn-sm" onclick="fetchPublicHolidays()">
                                <i class="fas fa-cloud-download-alt me-1"></i>
                                获取公共节假日
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="importHolidays()">
                                <i class="fas fa-upload me-1"></i>
                                批量导入
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="exportHolidays()">
                                <i class="fas fa-download me-1"></i>
                                导出数据
                            </button>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="resetCalendar()">
                                <i class="fas fa-refresh me-1"></i>
                                重置日历
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10">
                <!-- 页面标题和控制栏 -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-calendar-alt me-2"></i>节假日日历管理</h2>
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <select class="form-select" id="yearSelector" onchange="changeYear()">
                                <!-- 年份选项将通过JavaScript动态生成 -->
                            </select>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="refreshCalendar()">
                                <i class="fas fa-refresh me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="showCurrentMonth()">
                                <i class="fas fa-calendar-day me-1"></i>回到当月
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary" id="totalDays">365</h5>
                                <p class="card-text">总天数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success" id="workDays">245</h5>
                                <p class="card-text">工作日</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning" id="weekendDays">104</h5>
                                <p class="card-text">双休日</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger" id="holidayDays">16</h5>
                                <p class="card-text">节假日</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日历容器 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" id="calendarTitle">2025年节假日日历</h6>
                        <small class="text-muted">点击日期可设置节假日，红色为节假日，绿色为调休工作日</small>
                    </div>
                    <div class="card-body">
                        <div id="yearCalendar" class="year-calendar-container">
                            <!-- 月历将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日期详情模态框 -->
    <div class="modal fade" id="dateDetailModal" tabindex="-1" aria-labelledby="dateDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dateDetailModalLabel">
                        <i class="fas fa-calendar-day me-2"></i>日期详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>日期：</strong></td>
                                    <td id="modalDate">-</td>
                                </tr>
                                <tr>
                                    <td><strong>星期：</strong></td>
                                    <td id="modalWeekday">-</td>
                                </tr>
                                <tr>
                                    <td><strong>类型：</strong></td>
                                    <td id="modalDayType">-</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>节假日设置</h6>
                            <form id="holidaySettingForm">
                                <input type="hidden" id="modalDateValue">
                                <div class="mb-3">
                                    <label for="modalHolidayType" class="form-label">节假日类型</label>
                                    <select class="form-select" id="modalHolidayType">
                                        <option value="">普通日期</option>
                                        <option value="1">法定节假日</option>
                                        <option value="2">调休工作日</option>
                                        <option value="3">自定义休息日</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="modalHolidayName" class="form-label">节假日名称</label>
                                    <input type="text" class="form-control" id="modalHolidayName" placeholder="如：春节、劳动节等">
                                </div>
                                <div class="mb-3">
                                    <label for="modalRemark" class="form-label">备注</label>
                                    <textarea class="form-control" id="modalRemark" rows="2" placeholder="可选备注信息"></textarea>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-danger" id="removeHolidayBtn" onclick="removeHoliday()" style="display: none;">
                        <i class="fas fa-trash me-1"></i>移除节假日
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveHolidaySetting()">
                        <i class="fas fa-save me-1"></i>保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="importModalLabel">
                        <i class="fas fa-upload me-2"></i>批量导入节假日
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="importFile" class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls">
                        <div class="form-text">支持.xlsx和.xls格式，请按照模板格式填写数据</div>
                    </div>
                    <div class="mb-3">
                        <a href="#" class="btn btn-outline-info btn-sm" onclick="downloadTemplate()">
                            <i class="fas fa-download me-1"></i>下载导入模板
                        </a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="uploadFile()">
                        <i class="fas fa-upload me-1"></i>开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 获取公共节假日确认模态框 -->
    <div class="modal fade" id="fetchHolidaysModal" tabindex="-1" aria-labelledby="fetchHolidaysModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fetchHolidaysModalLabel">
                        <i class="fas fa-cloud-download-alt me-2"></i>获取公共节假日
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        将从公共API获取指定年份的法定节假日信息，这将覆盖现有的法定节假日设置。
                    </div>
                    <div class="mb-3">
                        <label for="fetchYear" class="form-label">选择年份</label>
                        <select class="form-select" id="fetchYear">
                            <!-- 年份选项将通过JavaScript动态生成 -->
                        </select>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="overwriteExisting" checked>
                        <label class="form-check-label" for="overwriteExisting">
                            覆盖现有的法定节假日设置
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="confirmFetchHolidays()">
                        <i class="fas fa-download me-1"></i>开始获取
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="js/holiday-calendar.js"></script>
</body>
</html>