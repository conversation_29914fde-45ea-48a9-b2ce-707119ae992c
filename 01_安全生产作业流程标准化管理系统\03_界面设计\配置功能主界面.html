<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全生产作业流程配置系统 - 主界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .nav-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .nav-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
            border-color: #007bff;
        }
        
        .nav-card.primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
        }
        
        .nav-card.secondary {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
            border: none;
        }
        
        .nav-card-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .nav-card h3 {
            font-size: 20px;
            margin-bottom: 10px;
        }
        
        .nav-card p {
            font-size: 14px;
            opacity: 0.8;
            line-height: 1.5;
        }
        
        .status-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .status-header h2 {
            color: #2c3e50;
            font-size: 24px;
        }
        
        .library-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .config-item {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .config-item.warning {
            border-left-color: #ffc107;
        }
        
        .config-item.danger {
            border-left-color: #dc3545;
        }
        
        .config-item-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .config-item-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .config-item h4 {
            color: #2c3e50;
            font-size: 16px;
        }
        
        .config-item-status {
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .config-item-stats {
            font-size: 12px;
            color: #6c757d;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-badge.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-badge.danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .action-btn {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #2c3e50;
        }
        
        .action-btn:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        
        .action-btn-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }
        
        .system-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ 安全生产作业流程配置系统</h1>
            <p>朝阳库点 - 中型库点 | 管理员：张三 | 最后更新：2024-01-15</p>
        </div>
        
        <div class="main-content">
            <!-- 主要功能导航 -->
            <div class="nav-cards">
                <div class="nav-card primary" onclick="location.href='快速配置向导.html'">
                    <span class="nav-card-icon">🎯</span>
                    <h3>快速配置向导</h3>
                    <p>3步完成配置，10分钟搞定新作业类型</p>
                </div>
                
                <div class="nav-card secondary" onclick="location.href='模板切换管理.html'">
                    <span class="nav-card-icon">🔄</span>
                    <h3>模板切换管理</h3>
                    <p>一键切换不同复杂度的作业模板</p>
                </div>
                
                <div class="nav-card" onclick="location.href='预置模板库.html'">
                    <span class="nav-card-icon">📋</span>
                    <h3>预置模板库</h3>
                    <p>查看所有可用的标准作业模板</p>
                </div>
                
                <div class="nav-card">
                    <span class="nav-card-icon">⚙️</span>
                    <h3>库点配置状态</h3>
                    <p>查看当前配置完成度和使用统计</p>
                </div>
            </div>
            
            <!-- 当前库点配置状态 -->
            <div class="status-section">
                <div class="status-header">
                    <h2>📍 当前库点配置状态</h2>
                    <div class="library-info">
                        <strong>朝阳库点</strong> | 中型库点 | 存储容量：3.2万吨 | 员工：45人
                    </div>
                </div>
                
                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-item-header">
                            <span class="config-item-icon">🔥</span>
                            <h4>动火作业</h4>
                        </div>
                        <div class="config-item-status">
                            <span class="status-badge success">已配置</span> 标准版模板A + 定制扩展
                        </div>
                        <div class="config-item-stats">
                            本月申请：156次 | 平均处理：1.8小时 | 满意度：92%
                        </div>
                    </div>
                    
                    <div class="config-item">
                        <div class="config-item-header">
                            <span class="config-item-icon">🏠</span>
                            <h4>有限空间作业</h4>
                        </div>
                        <div class="config-item-status">
                            <span class="status-badge success">已配置</span> 标准版模板A
                        </div>
                        <div class="config-item-stats">
                            本月申请：45次 | 平均处理：3.2小时 | 满意度：88%
                        </div>
                    </div>
                    
                    <div class="config-item warning">
                        <div class="config-item-header">
                            <span class="config-item-icon">⬆️</span>
                            <h4>高处作业</h4>
                        </div>
                        <div class="config-item-status">
                            <span class="status-badge success">已配置</span> 简化版模板C
                        </div>
                        <div class="config-item-stats">
                            本月申请：12次 | 平均处理：0.8小时 | 满意度：95%
                        </div>
                    </div>
                    
                    <div class="config-item danger">
                        <div class="config-item-header">
                            <span class="config-item-icon">🔧</span>
                            <h4>设备检修</h4>
                        </div>
                        <div class="config-item-status">
                            <span class="status-badge danger">未配置</span> 急需配置
                        </div>
                        <div class="config-item-stats">
                            本月申请：38次 | 当前：手工流程 | 影响效率
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快速操作 -->
            <div class="quick-actions">
                <a href="#" class="action-btn">
                    <span class="action-btn-icon">🚀</span>
                    <div>配置设备检修</div>
                </a>
                <a href="#" class="action-btn">
                    <span class="action-btn-icon">🔧</span>
                    <div>优化现有配置</div>
                </a>
                <a href="#" class="action-btn">
                    <span class="action-btn-icon">📱</span>
                    <div>移动端预览</div>
                </a>
                <a href="#" class="action-btn">
                    <span class="action-btn-icon">📊</span>
                    <div>使用统计报告</div>
                </a>
            </div>
            
            <!-- 系统状态统计 -->
            <div class="system-stats">
                <div class="stat-card">
                    <div class="stat-value">75%</div>
                    <div class="stat-label">配置完成度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">251</div>
                    <div class="stat-label">本月作业申请</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">2.1h</div>
                    <div class="stat-label">平均处理时间</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">91%</div>
                    <div class="stat-label">整体满意度</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
