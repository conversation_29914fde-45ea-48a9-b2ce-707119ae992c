<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤6：效果检查 - WEB端+APP端协同</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        .step-container { display: grid; grid-template-columns: 1fr 350px; gap: 20px; margin-top: 20px; }
        .platform-tabs { display: flex; background: white; border-radius: 8px; padding: 5px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); }
        .platform-tab { flex: 1; padding: 12px; text-align: center; border-radius: 6px; cursor: pointer; transition: all 0.3s; }
        .platform-tab.active { background: #4CAF50; color: white; }
        .sampling-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0; }
        .sampling-point { border: 2px solid #e0e0e0; border-radius: 8px; padding: 15px; text-align: center; cursor: pointer; transition: all 0.3s; }
        .sampling-point:hover { border-color: #4CAF50; }
        .sampling-point.completed { border-color: #4CAF50; background: #f8fff8; }
        .test-results { background: #f8f9fa; border-radius: 8px; padding: 20px; margin: 15px 0; }
        .result-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #e0e0e0; }
        .result-item:last-child { border-bottom: none; }
        .result-value { font-weight: bold; }
        .result-pass { color: #4CAF50; }
        .result-fail { color: #f44336; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div>
                    <div class="header-title">步骤6：效果检查</div>
                    <div class="header-subtitle">WEB端+APP端协同 - 采样检测与效果评估</div>
                </div>
                <div class="header-user">
                    <div class="user-info">
                        <div class="user-name">赵六</div>
                        <div class="user-role">检验员</div>
                    </div>
                    <div class="user-avatar">赵</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 工作流程导航 -->
        <div class="workflow-container">
            <div class="workflow-steps" id="workflowSteps"></div>
        </div>

        <!-- 平台切换 -->
        <div class="platform-tabs">
            <div class="platform-tab active" onclick="switchPlatform('web')">WEB端 - 数据分析</div>
            <div class="platform-tab" onclick="switchPlatform('app')">APP端 - 现场采样</div>
        </div>

        <div class="step-container">
            <!-- WEB端内容 -->
            <div id="webContent">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">检验数据录入</div>
                        <div class="card-subtitle">录入实验室检测结果</div>
                    </div>
                    <div class="card-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">检测日期</label>
                                <input type="date" class="form-control" value="2024-01-23">
                            </div>
                            <div class="form-group">
                                <label class="form-label">检测人员</label>
                                <input type="text" class="form-control" value="赵六" readonly>
                            </div>
                        </div>
                        
                        <div class="test-results">
                            <h4>检测结果</h4>
                            <div class="result-item">
                                <span>活虫密度（头/kg）</span>
                                <span class="result-value result-pass">0.2</span>
                            </div>
                            <div class="result-item">
                                <span>死虫率（%）</span>
                                <span class="result-value result-pass">98.5</span>
                            </div>
                            <div class="result-item">
                                <span>PH3残留（mg/kg）</span>
                                <span class="result-value result-pass">0.05</span>
                            </div>
                            <div class="result-item">
                                <span>粮食水分（%）</span>
                                <span class="result-value result-pass">12.8</span>
                            </div>
                            <div class="result-item">
                                <span>粮食品质</span>
                                <span class="result-value result-pass">正常</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">效果评估</label>
                            <select class="form-control">
                                <option>优秀 - 达到预期效果</option>
                                <option>良好 - 基本达到效果</option>
                                <option>一般 - 需要改进</option>
                                <option>不合格 - 需要重新处理</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">检验报告</label>
                            <textarea class="form-control" rows="4" placeholder="详细描述检验结果和建议"></textarea>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
                        <button class="btn btn-primary" onclick="generateReport()">生成报告</button>
                    </div>
                </div>
            </div>

            <!-- APP端内容 -->
            <div id="appContent" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">现场采样</div>
                        <div class="card-subtitle">按照采样点进行样品采集</div>
                    </div>
                    <div class="card-body">
                        <div class="sampling-grid">
                            <div class="sampling-point completed" onclick="selectSamplingPoint(1)">
                                <div style="font-size: 24px; margin-bottom: 8px;">✓</div>
                                <div style="font-weight: bold;">采样点1</div>
                                <div style="font-size: 12px; color: #666;">东北角</div>
                            </div>
                            <div class="sampling-point completed" onclick="selectSamplingPoint(2)">
                                <div style="font-size: 24px; margin-bottom: 8px;">✓</div>
                                <div style="font-weight: bold;">采样点2</div>
                                <div style="font-size: 12px; color: #666;">东南角</div>
                            </div>
                            <div class="sampling-point completed" onclick="selectSamplingPoint(3)">
                                <div style="font-size: 24px; margin-bottom: 8px;">✓</div>
                                <div style="font-weight: bold;">采样点3</div>
                                <div style="font-size: 12px; color: #666;">西北角</div>
                            </div>
                            <div class="sampling-point" onclick="selectSamplingPoint(4)">
                                <div style="font-size: 24px; margin-bottom: 8px;">4</div>
                                <div style="font-weight: bold;">采样点4</div>
                                <div style="font-size: 12px; color: #666;">西南角</div>
                            </div>
                            <div class="sampling-point" onclick="selectSamplingPoint(5)">
                                <div style="font-size: 24px; margin-bottom: 8px;">5</div>
                                <div style="font-weight: bold;">采样点5</div>
                                <div style="font-size: 12px; color: #666;">中心点</div>
                            </div>
                        </div>

                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;">
                            <strong>采样进度：</strong>3/5 个采样点已完成
                        </div>

                        <div class="form-group">
                            <label class="form-label">当前采样点信息</label>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                <div><strong>位置：</strong>西南角</div>
                                <div><strong>深度：</strong>表层、中层、底层</div>
                                <div><strong>样品量：</strong>每层500g</div>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin: 15px 0;">
                            <div style="aspect-ratio: 4/3; border: 2px dashed #ddd; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer; background: #fafafa;" onclick="takePhoto('sampling')">
                                <div style="font-size: 24px; color: #999; margin-bottom: 8px;">📷</div>
                                <div style="font-size: 12px; color: #666; text-align: center;">采样过程</div>
                            </div>
                            <div style="aspect-ratio: 4/3; border: 2px dashed #ddd; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer; background: #fafafa;" onclick="takePhoto('sample')">
                                <div style="font-size: 24px; color: #999; margin-bottom: 8px;">📷</div>
                                <div style="font-size: 12px; color: #666; text-align: center;">样品照片</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-primary" onclick="completeSampling()">完成采样</button>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div>
                <div class="card">
                    <div class="card-title">检查进度</div>
                    <div style="margin: 15px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>现场采样</span>
                            <span style="color: #4CAF50; font-weight: bold;">60%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 60%"></div>
                        </div>
                    </div>
                    <div style="margin: 15px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>实验室检测</span>
                            <span style="color: #ff9800; font-weight: bold;">80%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 80%"></div>
                        </div>
                    </div>
                    <div style="margin: 15px 0;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>报告生成</span>
                            <span style="color: #666; font-weight: bold;">0%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">质量标准</div>
                    <div style="font-size: 12px; color: #666; line-height: 1.6;">
                        <div><strong>活虫密度：</strong>≤ 2头/kg</div>
                        <div><strong>死虫率：</strong>≥ 95%</div>
                        <div><strong>PH3残留：</strong>≤ 0.1mg/kg</div>
                        <div><strong>水分变化：</strong>≤ ±0.5%</div>
                        <div><strong>品质要求：</strong>无异味、无变色</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/common.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeWorkflowSteps();
        });

        // 初始化工作流程步骤
        function initializeWorkflowSteps() {
            const stepsContainer = document.getElementById('workflowSteps');
            stepsContainer.innerHTML = '';

            WORKFLOW_STEPS.forEach(step => {
                const stepElement = document.createElement('div');
                stepElement.className = `workflow-step ${step.id === 6 ? 'active' : step.status}`;
                stepElement.onclick = () => navigateToStep(step.id);
                
                stepElement.innerHTML = `
                    <div class="step-circle">${step.id === 6 ? step.id : (step.status === 'completed' ? '✓' : step.id)}</div>
                    <div class="step-title">${step.name}</div>
                    <div class="step-time">${step.id === 6 ? '进行中...' : getStepTime(step)}</div>
                `;
                
                stepsContainer.appendChild(stepElement);
            });
        }

        function getStepTime(step) {
            return step.status === 'completed' ? '已完成' : '待执行';
        }

        function navigateToStep(stepId) {
            if (stepId === 6) return;
            if (stepId < 6 && WORKFLOW_STEPS[stepId - 1].status === 'completed') {
                window.location.href = `step${stepId}.html`;
            } else if (stepId > 6) {
                NotificationManager.warning('请先完成当前步骤');
            }
        }

        // 平台切换
        function switchPlatform(platform) {
            document.querySelectorAll('.platform-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            if (platform === 'web') {
                document.getElementById('webContent').style.display = 'block';
                document.getElementById('appContent').style.display = 'none';
            } else {
                document.getElementById('webContent').style.display = 'none';
                document.getElementById('appContent').style.display = 'block';
            }
        }

        // 选择采样点
        function selectSamplingPoint(pointId) {
            NotificationManager.info(`选择采样点${pointId}`);
        }

        // 拍照
        function takePhoto(type) {
            NotificationManager.success('照片已保存');
        }

        // 完成采样
        function completeSampling() {
            NotificationManager.success('采样工作已完成');
        }

        // 保存草稿
        function saveDraft() {
            NotificationManager.success('检验数据已保存');
        }

        // 生成报告
        function generateReport() {
            workflowManager.completeStep(6, { status: 'completed' });
            NotificationManager.success('检验报告已生成！');
            setTimeout(() => window.location.href = 'step7.html', 2000);
        }

        // 返回按钮
        const backButton = document.createElement('button');
        backButton.className = 'btn btn-secondary';
        backButton.textContent = '← 返回主页';
        backButton.onclick = () => window.location.href = 'index.html';
        backButton.style.position = 'fixed';
        backButton.style.bottom = '20px';
        backButton.style.left = '20px';
        backButton.style.zIndex = '1000';
        document.body.appendChild(backButton);
    </script>
</body>
</html>
