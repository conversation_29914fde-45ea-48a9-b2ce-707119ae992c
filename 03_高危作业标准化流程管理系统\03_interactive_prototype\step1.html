<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步骤1：熏蒸方案制定 - WEB端</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        .step-container {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
            margin-top: 20px;
        }
        
        .plan-templates {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .template-card {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .template-card:hover {
            border-color: #4CAF50;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
        }
        
        .template-card.selected {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        
        .template-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .template-desc {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }
        
        .template-params {
            font-size: 12px;
            color: #888;
        }
        
        .calculation-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .calc-row {
            display: grid;
            grid-template-columns: 1fr auto 1fr auto 1fr;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .calc-operator {
            text-align: center;
            font-weight: bold;
            color: #666;
        }
        
        .calc-result {
            background: #4CAF50;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            text-align: center;
        }
        
        .approval-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .approval-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
        }
        
        .approval-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }
        
        .approval-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .approval-step.completed .approval-avatar {
            background: #4CAF50;
            color: white;
        }
        
        .approval-step.active .approval-avatar {
            background: #2196F3;
            color: white;
        }
        
        .approval-name {
            font-size: 12px;
            text-align: center;
        }
        
        .approval-time {
            font-size: 10px;
            color: #666;
        }
        
        .info-panel {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            height: fit-content;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            color: #666;
            font-size: 14px;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }
        
        .risk-assessment {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .risk-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .risk-level {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .risk-high {
            background: #ffebee;
            color: #c62828;
        }
        
        .risk-medium {
            background: #fff3e0;
            color: #ef6c00;
        }
        
        .risk-low {
            background: #e8f5e8;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div>
                    <div class="header-title">步骤1：熏蒸方案制定</div>
                    <div class="header-subtitle">WEB端 - 智能方案生成与审批管理</div>
                </div>
                <div class="header-user">
                    <div class="user-info">
                        <div class="user-name">张三</div>
                        <div class="user-role">作业负责人</div>
                    </div>
                    <div class="user-avatar">张</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 工作流程导航 -->
        <div class="workflow-container">
            <div class="workflow-steps" id="workflowSteps">
                <!-- 动态生成 -->
            </div>
        </div>

        <div class="step-container">
            <!-- 主要内容区域 -->
            <div>
                <!-- 方案模板选择 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">选择熏蒸方案模板</div>
                        <div class="card-subtitle">根据粮食品种和虫害情况选择合适的方案模板</div>
                    </div>
                    <div class="card-body">
                        <div class="plan-templates">
                            <div class="template-card selected" onclick="selectTemplate('wheat_standard')">
                                <div class="template-title">小麦标准熏蒸方案</div>
                                <div class="template-desc">适用于小麦的常规熏蒸作业，使用磷化铝熏蒸剂</div>
                                <div class="template-params">用药量：3g/m³ | 熏蒸期：7天 | 适用温度：15-30℃</div>
                            </div>
                            <div class="template-card" onclick="selectTemplate('wheat_intensive')">
                                <div class="template-title">小麦强化熏蒸方案</div>
                                <div class="template-desc">适用于虫害严重的小麦，加大用药量和延长熏蒸期</div>
                                <div class="template-params">用药量：4g/m³ | 熏蒸期：10天 | 适用温度：20-35℃</div>
                            </div>
                            <div class="template-card" onclick="selectTemplate('custom')">
                                <div class="template-title">自定义方案</div>
                                <div class="template-desc">根据具体情况自定义熏蒸参数</div>
                                <div class="template-params">手动设置所有参数</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 方案参数配置 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">方案参数配置</div>
                        <div class="card-subtitle">配置具体的熏蒸参数</div>
                    </div>
                    <div class="card-body">
                        <form id="planForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">仓房编号</label>
                                    <select class="form-control" name="warehouseId" required>
                                        <option value="1号仓" selected>1号仓</option>
                                        <option value="2号仓">2号仓</option>
                                        <option value="3号仓">3号仓</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">粮食品种</label>
                                    <select class="form-control" name="grainType" required onchange="updateCalculation()">
                                        <option value="小麦" selected>小麦</option>
                                        <option value="玉米">玉米</option>
                                        <option value="大豆">大豆</option>
                                        <option value="稻谷">稻谷</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">粮食数量（吨）</label>
                                    <input type="number" class="form-control" name="grainQuantity" value="500" required onchange="updateCalculation()">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">仓房体积（m³）</label>
                                    <input type="number" class="form-control" name="warehouseVolume" value="2000" readonly>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">害虫种类</label>
                                    <select class="form-control" name="pestType" required>
                                        <option value="玉米象" selected>玉米象</option>
                                        <option value="米象">米象</option>
                                        <option value="谷蠹">谷蠹</option>
                                        <option value="锯谷盗">锯谷盗</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">虫害密度（头/kg）</label>
                                    <input type="number" class="form-control" name="pestDensity" value="15.2" step="0.1" required onchange="updateCalculation()">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label required">熏蒸剂类型</label>
                                    <select class="form-control" name="fumigantType" required onchange="updateCalculation()">
                                        <option value="磷化铝" selected>磷化铝</option>
                                        <option value="磷化镁">磷化镁</option>
                                        <option value="磷化锌">磷化锌</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">熏蒸周期（小时）</label>
                                    <input type="number" class="form-control" name="fumigationPeriod" value="168" required>
                                </div>
                            </div>
                        </form>

                        <!-- 用药量计算 -->
                        <div class="calculation-panel">
                            <h4>用药量自动计算</h4>
                            <div class="calc-row">
                                <div>仓房体积：<strong id="calcVolume">2000</strong> m³</div>
                                <div class="calc-operator">×</div>
                                <div>用药浓度：<strong id="calcConcentration">3.0</strong> g/m³</div>
                                <div class="calc-operator">=</div>
                                <div class="calc-result" id="calcResult">6.0 kg</div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-top: 10px;">
                                * 用药浓度根据粮食品种、害虫种类和虫害密度自动调整
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
                        <button class="btn btn-primary" onclick="submitForApproval()">提交审批</button>
                    </div>
                </div>

                <!-- 审批流程 -->
                <div class="approval-section">
                    <h4>审批流程</h4>
                    <div class="approval-flow">
                        <div class="approval-step completed">
                            <div class="approval-avatar">张</div>
                            <div class="approval-name">张三<br>作业负责人</div>
                            <div class="approval-time">已提交</div>
                        </div>
                        <div style="flex: 0.5; height: 2px; background: #4CAF50;"></div>
                        <div class="approval-step active">
                            <div class="approval-avatar">李</div>
                            <div class="approval-name">李四<br>安全员</div>
                            <div class="approval-time">待审批</div>
                        </div>
                        <div style="flex: 0.5; height: 2px; background: #ddd;"></div>
                        <div class="approval-step">
                            <div class="approval-avatar">王</div>
                            <div class="approval-name">王五<br>安全主管</div>
                            <div class="approval-time">待审批</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px;">
                        <strong>当前状态：</strong>
                        <span class="badge badge-warning">安全员审批中</span>
                        <span style="margin-left: 15px; font-size: 12px; color: #666;">
                            预计审批完成时间：2024-01-16 17:00
                        </span>
                    </div>
                </div>
            </div>

            <!-- 侧边栏信息 -->
            <div>
                <!-- 基本信息 -->
                <div class="info-panel">
                    <div class="card-title">作业基本信息</div>
                    <div class="info-item">
                        <span class="info-label">申请编号</span>
                        <span class="info-value">FUM-2024-001</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">申请人</span>
                        <span class="info-value">张三</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">申请时间</span>
                        <span class="info-value">2024-01-15 09:30</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">计划开始</span>
                        <span class="info-value">2024-01-16 08:00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">计划结束</span>
                        <span class="info-value">2024-01-23 18:00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">风险等级</span>
                        <span class="info-value">
                            <span class="badge badge-danger">高风险</span>
                        </span>
                    </div>
                </div>

                <!-- 风险评估 -->
                <div class="risk-assessment">
                    <div class="card-title">风险评估</div>
                    <div class="risk-item">
                        <span>人员安全风险</span>
                        <span class="risk-level risk-high">高</span>
                    </div>
                    <div class="risk-item">
                        <span>环境影响风险</span>
                        <span class="risk-level risk-medium">中</span>
                    </div>
                    <div class="risk-item">
                        <span>设备故障风险</span>
                        <span class="risk-level risk-low">低</span>
                    </div>
                    <div class="risk-item">
                        <span>粮食质量风险</span>
                        <span class="risk-level risk-low">低</span>
                    </div>
                    <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px; font-size: 12px;">
                        <strong>风险提示：</strong>磷化氢气体有毒，作业过程中必须严格按照安全操作规程执行，确保人员防护到位。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/common.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeWorkflowSteps();
            updateCalculation();
            initializeFormValidation();
        });

        // 初始化工作流程步骤
        function initializeWorkflowSteps() {
            const stepsContainer = document.getElementById('workflowSteps');
            stepsContainer.innerHTML = '';

            WORKFLOW_STEPS.forEach(step => {
                const stepElement = document.createElement('div');
                stepElement.className = `workflow-step ${step.id === 1 ? 'active' : step.status}`;
                stepElement.onclick = () => navigateToStep(step.id);
                
                stepElement.innerHTML = `
                    <div class="step-circle">${step.id === 1 ? step.id : (step.status === 'completed' ? '✓' : step.id)}</div>
                    <div class="step-title">${step.name}</div>
                    <div class="step-time">${step.id === 1 ? '进行中...' : getStepTime(step)}</div>
                `;
                
                stepsContainer.appendChild(stepElement);
            });
        }

        // 获取步骤时间
        function getStepTime(step) {
            if (step.status === 'completed') {
                return '已完成';
            } else {
                return '待执行';
            }
        }

        // 导航到步骤
        function navigateToStep(stepId) {
            if (stepId === 1) return; // 当前步骤
            
            if (stepId > 1) {
                NotificationManager.warning('请先完成当前步骤的审批流程');
                return;
            }
            
            window.location.href = `step${stepId}.html`;
        }

        // 选择方案模板
        function selectTemplate(templateId) {
            // 移除其他选中状态
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('selected');
            
            // 根据模板更新参数
            const templates = {
                'wheat_standard': {
                    concentration: 3.0,
                    period: 168,
                    description: '小麦标准熏蒸方案'
                },
                'wheat_intensive': {
                    concentration: 4.0,
                    period: 240,
                    description: '小麦强化熏蒸方案'
                },
                'custom': {
                    concentration: 3.0,
                    period: 168,
                    description: '自定义方案'
                }
            };
            
            const template = templates[templateId];
            if (template) {
                document.querySelector('[name="fumigationPeriod"]').value = template.period;
                updateCalculation();
                NotificationManager.success(`已选择：${template.description}`);
            }
        }

        // 更新用药量计算
        function updateCalculation() {
            const volume = parseFloat(document.querySelector('[name="warehouseVolume"]').value) || 2000;
            const grainType = document.querySelector('[name="grainType"]').value;
            const pestDensity = parseFloat(document.querySelector('[name="pestDensity"]').value) || 15.2;
            const fumigantType = document.querySelector('[name="fumigantType"]').value;
            
            // 根据条件计算用药浓度
            let concentration = 3.0; // 基础浓度
            
            // 根据虫害密度调整
            if (pestDensity > 20) {
                concentration += 0.5;
            } else if (pestDensity > 10) {
                concentration += 0.2;
            }
            
            // 根据粮食品种调整
            if (grainType === '玉米') {
                concentration += 0.2;
            } else if (grainType === '大豆') {
                concentration += 0.3;
            }
            
            // 根据熏蒸剂类型调整
            if (fumigantType === '磷化镁') {
                concentration *= 1.1;
            }
            
            const totalDosage = (volume * concentration / 1000).toFixed(1); // 转换为kg
            
            // 更新显示
            document.getElementById('calcVolume').textContent = volume;
            document.getElementById('calcConcentration').textContent = concentration.toFixed(1);
            document.getElementById('calcResult').textContent = totalDosage + ' kg';
        }

        // 初始化表单验证
        function initializeFormValidation() {
            const form = document.getElementById('planForm');
            const validator = new FormValidator(form);
            
            validator
                .required('warehouseId', '请选择仓房编号')
                .required('grainType', '请选择粮食品种')
                .required('grainQuantity', '请输入粮食数量')
                .required('pestType', '请选择害虫种类')
                .required('pestDensity', '请输入虫害密度')
                .required('fumigantType', '请选择熏蒸剂类型')
                .required('fumigationPeriod', '请输入熏蒸周期');
            
            window.planFormValidator = validator;
        }

        // 保存草稿
        function saveDraft() {
            const formData = new FormData(document.getElementById('planForm'));
            const planData = Object.fromEntries(formData.entries());
            
            // 添加计算结果
            planData.calculatedDosage = document.getElementById('calcResult').textContent;
            planData.concentration = document.getElementById('calcConcentration').textContent;
            
            // 保存到本地存储
            workflowManager.setStepData(1, {
                ...planData,
                status: 'draft',
                savedAt: new Date().toISOString()
            });
            
            NotificationManager.success('草稿已保存');
        }

        // 提交审批
        function submitForApproval() {
            if (!window.planFormValidator.validate()) {
                NotificationManager.error('请填写所有必填项');
                return;
            }
            
            const formData = new FormData(document.getElementById('planForm'));
            const planData = Object.fromEntries(formData.entries());
            
            // 添加计算结果
            planData.calculatedDosage = document.getElementById('calcResult').textContent;
            planData.concentration = document.getElementById('calcConcentration').textContent;
            
            // 保存数据并更新状态
            workflowManager.setStepData(1, {
                ...planData,
                status: 'submitted',
                submittedAt: new Date().toISOString()
            });
            
            // 模拟提交过程
            const submitBtn = event.target;
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';
            
            setTimeout(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = '提交审批';
                
                NotificationManager.success('方案已提交审批，请等待审批结果');
                
                // 更新审批状态显示
                updateApprovalStatus();
            }, 2000);
        }

        // 更新审批状态
        function updateApprovalStatus() {
            // 这里可以添加更新审批状态的逻辑
            console.log('审批状态已更新');
        }

        // 返回主页
        function goBack() {
            window.location.href = 'index.html';
        }

        // 添加返回按钮到页面
        const backButton = document.createElement('button');
        backButton.className = 'btn btn-secondary';
        backButton.textContent = '← 返回主页';
        backButton.onclick = goBack;
        backButton.style.position = 'fixed';
        backButton.style.bottom = '20px';
        backButton.style.left = '20px';
        backButton.style.zIndex = '1000';
        document.body.appendChild(backButton);

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 's':
                        e.preventDefault();
                        saveDraft();
                        break;
                    case 'Enter':
                        e.preventDefault();
                        submitForApproval();
                        break;
                }
            }

            if (e.key === 'Escape') {
                goBack();
            }
        });
    </script>
</body>
</html>
