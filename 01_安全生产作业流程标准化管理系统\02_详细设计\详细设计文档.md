# 安全生产作业流程系统 - 配置功能详细设计文档

## 1. 概述

### 1.1 设计目标
基于需求分析，设计一套高度灵活、可配置的安全生产作业流程管理系统，重点解决不同地区、不同库点的差异化需求，同时确保国家安全标准的统一执行。

### 1.2 核心设计理念
- **配置驱动**：通过配置而非代码实现业务逻辑
- **分层继承**：三层模板体系确保标准化与灵活性平衡
- **可视化操作**：非技术人员也能进行配置管理
- **版本管理**：支持配置的版本控制和回滚机制

## 2. 系统整体架构

### 2.1 架构层次
```
用户层：Web管理端、移动作业端、监控大屏
    ↓
应用服务层：用户权限服务、流程引擎服务、表单引擎服务、作业管理服务
    ↓
核心引擎层：流程模板引擎、表单配置引擎、规则引擎、工作流引擎
    ↓
数据存储层：业务数据库、流程数据库、文件存储、缓存层
    ↓
外部集成：监控设备API、视频监控系统、短信邮件服务
```

### 2.2 三层模板体系
- **国家标准层**：基础安全要求，强制性节点，不可修改
- **区域定制层**：省级/市级规范，可增加节点和字段
- **库点执行层**：具体仓库定制，可调整执行细节

## 3. 配置功能设计

### 3.1 配置管理中心
包含七大核心配置功能：
- 组织架构配置
- 作业类型配置  
- 流程模板配置
- 表单模板配置
- 权限角色配置
- 安全规则配置
- 系统参数配置

## 4. 表单模板配置界面设计

### 4.1 表单设计器布局
**左侧组件库**：
- 基础组件：文本输入框、数字输入框、下拉选择框、单选按钮组、多选框组、日期选择器、时间选择器、文本域
- 移动组件：文件上传、拍照组件、位置定位、扫码组件、签名组件、语音录入
- 布局组件：分组容器、标签页、折叠面板、分割线

**中间设计区域**：
以动火作业申请表单为例，包含五大模块：
1. 📋 作业基本信息：作业标题、作业类型、作业地点、计划时间
2. 👥 人员配置：申请人、作业负责人、作业人员清单、监护人、资质证书上传
3. ⚠️ 风险评估：风险等级评估、主要风险点描述、风险照片上传、环境条件评估
4. 🛡️ 安全措施：消防器材准备、隔离措施说明、应急预案、个人防护用品
5. 📍 现场确认：作业现场定位、现场环境照片、申请人签名

**右侧属性面板**：
- 基本属性：字段名称、字段标识、字段描述、是否必填
- 显示属性：显示条件、默认值、占位符、帮助文本
- 验证规则：数据类型、长度限制、数值范围、正则表达式
- 权限控制：可见角色、可编辑角色、只读条件

### 4.2 表单模板三层继承体系
**国家标准层表单（不可修改）**：
- 作业标题、作业地点、计划时间、申请人、风险等级、安全措施、申请人签名等基础必填字段

**区域定制层表单（可增加字段）**：
- 继承国标所有字段
- 增加：环保审批意见、周边影响评估、区域负责人确认、特殊天气限制、应急联系人

**库点执行层表单（可调整细节）**：
- 继承区域所有字段
- 增加：库点设备清单、现场照片（3张）、GPS定位坐标、监护人手机号
- 调整：风险等级选项、安全措施模板

### 4.3 表单配置功能
- **模板管理**：创建新模板、复制现有模板、版本管理、发布审批
- **字段管理**：添加字段、修改属性、设置验证、权限控制
- **预览测试**：PC端预览、移动端预览、表单验证测试、数据提交测试

## 5. 流程模板配置界面设计

### 5.1 可视化流程设计器
**工具栏组件**：
- 🟢 开始节点、📝 用户任务、⚙️ 服务任务、✅ 审批任务
- ❓ 网关节点、⏰ 定时任务、📧 消息任务、🔴 结束节点

**动火作业流程设计示例**：
```
开始：提交申请 → 风险等级评估 → 分级审批（总经理/安全主管/部门主管）
→ 制定安全措施 → 人员资质确认 → 现场安全检查 → 消防器材准备 
→ 气体检测 → 检测结果判断 → 开始作业确认 → 实时监护 
→ 作业状态检查 → 现场清理检查 → 作业验收 → 记录归档 → 流程结束
```

**节点属性配置面板**：
- 基本信息：节点名称、节点类型、节点描述
- 处理人设置：指定用户、指定角色、指定部门、动态分配
- 时限设置：处理时限、提醒时间、超时处理
- 表单配置：关联表单、字段权限、必填验证
- 流转条件：条件表达式、分支规则、回退规则
- 通知设置：消息模板、通知方式、通知对象

### 5.2 流程模板三层继承与版本管理
**国家标准流程模板**：
- 强制节点：申请提交、风险评估、安全审批、现场检查、作业执行、作业验收、记录归档

**区域定制流程模板**：
- 继承国标所有强制节点
- 增加：环保部门审批、周边单位通知、区域负责人确认
- 调整：审批时限要求、检查标准细化

**库点执行流程模板**：
- 继承区域所有节点
- 增加：库点设备检查、现场拍照确认、GPS定位记录、实时视频监控
- 调整：监护人配置、应急联系方式

**版本管理功能**：
- 版本控制：版本创建、版本比较、版本回滚、版本发布
- 变更管理：变更申请、变更审批、变更通知、变更记录
- 影响分析：运行实例统计、影响范围评估、迁移方案制定、风险评估报告

**流程配置工具**：
- 可视化设计器：拖拽式设计、节点属性配置、连线条件设置、布局自动调整
- 流程测试：流程验证、模拟执行、性能测试、异常场景测试
- 导入导出：BPMN格式导出、JSON格式导出、模板批量导入、跨系统迁移

## 6. 作业类型配置界面设计

### 6.1 作业类型分类管理
**主要作业类型**：
- 🔥 动火作业（高风险）：总经理审批、专人监护、消防器材、焊工证、白天作业
- 🏠 有限空间作业（高风险）：总经理审批、现场监护、通风设备、特种作业证、气体检测
- ⬆️ 高处作业（中风险）：安全主管审批、地面监护、安全带、高处作业证、无强风
- 🔧 设备检修（中风险）：部门主管审批、技术监护、检修工具、维修证书、设备断电

### 6.2 配置属性详细设置
- **基本信息配置**：作业类型名称、编码、描述、适用范围、法规依据
- **风险等级配置**：风险等级定义、评估标准、控制措施、应急预案
- **审批流程配置**：审批层级设置、时限要求、条件判断、特殊情况处理
- **人员要求配置**：作业人员资质、监护人员要求、最少人员数量、培训要求
- **设备工具配置**：必备设备清单、检测仪器要求、防护用品清单、应急器材配置
- **安全条件配置**：环境条件要求、时间限制条件、天气条件限制、现场条件要求

### 6.3 关联配置管理
- **流程模板关联**：申请流程模板、审批流程模板、执行流程模板、验收流程模板
- **表单模板关联**：申请表单模板、检查表单模板、记录表单模板、验收表单模板
- **安全规则关联**：时间限制规则、环境条件规则、人员配置规则、设备检查规则

### 6.4 作业类型配置向导
7步配置流程：
1. 基本信息：名称、编码、风险等级、适用范围、法规依据
2. 人员要求：作业人员、监护人员、最少人数、培训要求、健康要求
3. 设备工具：消防器材、防护用品、检测设备、通讯设备、应急设备
4. 安全条件：时间限制、天气条件、环境要求、距离要求、检测标准
5. 流程关联：申请流程、审批流程、执行流程、验收流程、应急流程
6. 表单关联：申请表单、检查表单、记录表单、验收表单、事故表单
7. 预览确认：配置预览、规则验证、测试运行、保存发布

## 7. 安全规则配置界面设计

### 7.1 安全规则分类管理
**六大规则类型**：
- ⏰ **时间限制规则**：作业时间窗口（8:00-18:00）、禁止时段（夜间、节假日）、季节限制（高温、严寒期）、特殊时期（重大活动期间）
- 🌡️ **环境条件规则**：温度范围（-10°C ~ 40°C）、湿度限制（< 85%RH）、风速限制（< 5级风）、能见度要求（> 500米）
- 🧪 **检测标准规则**：可燃气体（< 25%LEL）、有毒气体（< 职业接触限值）、氧气浓度（19.5%-23.5%）、粉尘浓度（< 10mg/m³）
- 👥 **人员配置规则**：最少人员数（≥ 2人）、资质要求（持证上岗）、健康状态（体检合格）、培训要求（安全培训）
- 🛡️ **设备防护规则**：个人防护（防护用品齐全）、消防器材（配置到位）、检测设备（校验有效）、通讯设备（保持畅通）
- 📏 **安全距离规则**：作业距离（远离危险源）、隔离范围（设置警戒区）、疏散距离（应急疏散路径）、设备间距（符合规范要求）

### 7.2 规则配置功能
- **规则定义器**：条件表达式编辑器、逻辑运算符支持、函数库调用、变量引用管理
- **阈值管理器**：数值范围设置、枚举值配置、动态阈值计算、阈值分级管理
- **触发时机配置**：申请提交时检查、审批过程中检查、作业执行前检查、作业过程中监控、作业完成后验证
- **违规处理配置**：阻止操作、警告提示、强制确认、上报处理
- **例外管理**：紧急情况豁免、特殊审批流程、临时规则调整、例外记录追踪

### 7.3 规则应用场景
- **申请阶段验证**：基础条件检查、资质有效性验证、时间窗口验证、环境条件预检
- **审批阶段检查**：审批权限验证、审批条件检查、风险等级确认、特殊情况处理
- **执行阶段监控**：实时环境监测、人员状态检查、设备状态监控、安全距离监控
- **验收阶段确认**：作业质量检查、安全措施确认、现场清理验证、记录完整性检查

### 7.4 安全规则引擎配置界面
**规则编辑器组成**：
- 规则基本信息：规则名称、规则描述、规则类型、优先级、生效范围
- 条件配置：IF条件组，支持复杂逻辑表达式（AND、OR、NOT等）
- 动作配置：THEN动作组和ELSE动作组，定义规则触发后的处理动作
- 参数设置：变量定义、函数库、常量定义
- 测试验证：规则测试、调试功能
- 部署管理：版本控制、生效管理

**规则配置示例**：
```
规则名称：动火作业时间限制
IF (作业类型 = '动火作业' AND (当前时间 < 08:00 OR 当前时间 > 18:00 OR 节假日 = true OR 天气状况 IN ['雨','雪','大风']))
THEN (阻止提交申请 AND 显示错误信息 AND 记录违规日志 AND 发送通知消息)
ELSE (允许继续操作 AND 记录检查日志)
```

## 8. 权限角色配置界面设计

### 8.1 角色层级体系
- 🏛️ **系统管理员**：最高权限，全部数据访问，配置管理权限
- 🌏 **区域管理员**：区域范围权限，流程审批权限，报表统计权限
- 🏭 **库点管理员**：库点范围权限，作业执行权限，数据查看权限
- 👨‍💼 **安全主管**：安全审批权限，库点数据权限
- 👷‍♂️ **作业人员**：作业执行权限，个人数据权限
- 👁️ **监护人员**：监护确认权限，个人数据权限
- 📊 **查看人员**：只读查询权限，个人数据权限

### 8.2 权限配置功能
- **角色创建**：自定义角色
- **权限分配**：功能权限设置
- **数据范围**：数据权限设置
- **用户分配**：角色用户关联
- **权限继承**：上下级权限
- **权限审计**：操作日志

## 9. 组织架构配置界面设计

### 9.1 组织架构树形结构
```
国家粮食储备局（总部）
├── 华北区域（省级管理）
│   ├── 北京市粮库（市级管理）
│   │   ├── 朝阳库点（平房仓）
│   │   ├── 海淀库点（筒仓）
│   │   └── 丰台库点（浅圆仓）
│   └── 天津市粮库（市级管理）
│       ├── 滨海库点（立筒仓）
│       └── 武清库点（平房仓）
├── 华东区域（省级管理）
└── 华南区域（省级管理）
```

### 9.2 配置功能
- 📝 新增机构、✏️ 编辑信息、🔄 调整层级
- 👥 分配管理员、⚙️ 权限设置、📊 数据统计

### 9.3 机构属性配置
- **机构基本信息**：名称、编码、类型
- **联系方式**：地址、电话、负责人
- **业务范围**：仓储类型、容量规模
- **安全等级**：风险等级、监管要求

## 10. 配置功能业务价值

### 10.1 传统模式 vs 配置化方案对比

| 对比项目 | 传统模式 | 配置化方案 | 改善效果 |
|---------|---------|-----------|---------|
| 新库点部署 | 3-6个月定制开发 | 1-3天完成配置 | **效率提升90%** |
| 流程调整 | 需要开发人员修改代码 | 业务人员自主配置 | **成本降低80%** |
| 表单修改 | 需要编程实现 | 拖拽式表单设计 | **门槛降低95%** |
| 规则变更 | 需要发版上线 | 在线规则配置 | **响应速度提升95%** |
| 维护成本 | 高技术门槛 | 业务人员可维护 | **维护成本降低70%** |

### 10.2 业务价值体现
- 🚀 **实施效率提升**：部署周期缩短80%
- 💰 **成本大幅降低**：定制开发成本减少70%
- 🔧 **维护便捷性**：业务人员可自主配置
- 📈 **适应性增强**：快速响应业务变化
- 🎯 **标准化程度**：确保安全规范执行
- 📊 **管理精细化**：权限控制更加精准

### 10.3 应用场景示例
- **新库点上线**：1天完成配置
- **政策法规更新**：实时调整流程
- **区域差异适配**：灵活定制规则
- **人员变动调整**：快速权限配置

## 11. 技术实现要点

### 11.1 推荐技术栈
- **后端框架**：Spring Boot + MyBatis Plus
- **数据库**：MySQL（业务数据）+ Redis（缓存）
- **工作流引擎**：Flowable 或 Activiti
- **前端框架**：Vue.js + Element UI（管理端）+ Vant（移动端）
- **文件存储**：MinIO 或 阿里云OSS

### 11.2 关键技术点
1. **流程引擎集成**：使用BPMN 2.0标准定义流程
2. **动态表单**：基于JSON Schema生成表单
3. **多租户架构**：基于组织ID的数据隔离
4. **移动端适配**：PWA技术支持离线使用
5. **实时通信**：WebSocket推送流程状态变更
6. **JSON驱动**：所有配置以JSON格式存储，支持动态解析
7. **规则引擎**：基于Drools或自研规则引擎，支持复杂业务规则
8. **可视化设计**：基于Vue.js + Element UI的拖拽式设计器
9. **实时预览**：配置过程中可实时预览效果

## 12. 总结

本详细设计文档基于需求分析，重点设计了四大核心配置功能：表单模板配置、流程模板配置、作业类型配置、安全规则配置。通过三层模板体系（国家标准层→区域定制层→库点执行层）的设计，既保证了国家安全标准的统一执行，又满足了不同地区、不同库点的个性化需求。

配置化的设计理念大幅提升了系统的灵活性和可维护性，预期能够将新库点部署时间从数月缩短到数天，将定制开发成本降低70%以上，真正实现了"一次开发，处处配置"的目标。

该设计方案为向业主和领导展示系统的先进性和实用性提供了有力支撑，充分体现了系统的技术价值和商业价值。
