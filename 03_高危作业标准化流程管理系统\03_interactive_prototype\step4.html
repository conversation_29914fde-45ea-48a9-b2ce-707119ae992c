<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>步骤4：补药操作 - APP端</title>
    <link rel="stylesheet" href="assets/common.css">
    <style>
        body { background: #f7f8fa; padding-bottom: 80px; }
        .mobile-container { max-width: 480px; margin: 0 auto; padding: 15px; }
        .operation-card { background: white; border-radius: 12px; padding: 20px; margin-bottom: 15px; box-shadow: 0 2px 12px rgba(0,0,0,0.08); }
        .assessment-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
        .assessment-item { background: #f8f9fa; border-radius: 8px; padding: 15px; text-align: center; border-left: 4px solid #4CAF50; }
        .assessment-item.warning { border-left-color: #ff9800; background: #fff8e1; }
        .assessment-value { font-size: 20px; font-weight: bold; color: #333; margin-bottom: 4px; }
        .assessment-label { font-size: 12px; color: #666; }
        .supplement-form { background: #fff3cd; border-radius: 8px; padding: 20px; margin: 15px 0; }
        .fixed-bottom { position: fixed; bottom: 0; left: 0; right: 0; background: white; padding: 15px; box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1000; }
        .btn-group { display: flex; gap: 10px; max-width: 480px; margin: 0 auto; }
        .btn-full { flex: 1; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="header-content">
                <div>
                    <div class="header-title">步骤4：补药操作</div>
                    <div class="header-subtitle">APP端 - 效果评估与补药申请</div>
                </div>
                <div class="header-user">
                    <div class="user-info">
                        <div class="user-name">王五</div>
                        <div class="user-role">操作人员</div>
                    </div>
                    <div class="user-avatar">王</div>
                </div>
            </div>
        </div>
    </div>

    <div class="mobile-container">
        <!-- 熏蒸效果评估 -->
        <div class="operation-card">
            <div class="card-title">熏蒸效果评估</div>
            <div class="assessment-grid">
                <div class="assessment-item warning">
                    <div class="assessment-value">650</div>
                    <div class="assessment-label">PH3浓度(mg/m³)</div>
                </div>
                <div class="assessment-item">
                    <div class="assessment-value">72小时</div>
                    <div class="assessment-label">已熏蒸时间</div>
                </div>
                <div class="assessment-item warning">
                    <div class="assessment-value">75%</div>
                    <div class="assessment-label">预期效果达成</div>
                </div>
                <div class="assessment-item">
                    <div class="assessment-value">正常</div>
                    <div class="assessment-label">设备状态</div>
                </div>
            </div>
            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                <strong>评估结果：</strong>PH3浓度偏低，建议进行补药操作以确保熏蒸效果
            </div>
        </div>

        <!-- 补药申请 -->
        <div class="supplement-form">
            <div class="card-title">补药申请</div>
            <div class="form-group">
                <label class="form-label">补药原因</label>
                <select class="form-control">
                    <option>PH3浓度不足</option>
                    <option>局部区域效果差</option>
                    <option>虫害密度较高</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">建议补药量（kg）</label>
                <input type="number" class="form-control" value="3.2" step="0.1">
            </div>
            <div class="form-group">
                <label class="form-label">补药位置</label>
                <textarea class="form-control" rows="3" placeholder="描述需要补药的具体位置"></textarea>
            </div>
        </div>

        <!-- 现场拍照 -->
        <div class="operation-card">
            <div class="card-title">现场记录</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                <div style="aspect-ratio: 4/3; border: 2px dashed #ddd; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer; background: #fafafa;" onclick="takePhoto('before_supplement')">
                    <div style="font-size: 24px; color: #999; margin-bottom: 8px;">📷</div>
                    <div style="font-size: 12px; color: #666; text-align: center;">补药前状态</div>
                </div>
                <div style="aspect-ratio: 4/3; border: 2px dashed #ddd; border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer; background: #fafafa;" onclick="takePhoto('supplement_location')">
                    <div style="font-size: 24px; color: #999; margin-bottom: 8px;">📷</div>
                    <div style="font-size: 12px; color: #666; text-align: center;">补药位置</div>
                </div>
            </div>
        </div>
    </div>

    <div class="fixed-bottom">
        <div class="btn-group">
            <button class="btn btn-secondary btn-full" onclick="goBack()">返回</button>
            <button class="btn btn-warning btn-full" onclick="submitSupplement()">提交补药申请</button>
            <button class="btn btn-primary btn-full" onclick="skipSupplement()">跳过补药</button>
        </div>
    </div>

    <script src="assets/common.js"></script>
    <script>
        function takePhoto(type) {
            NotificationManager.success('照片已保存');
        }
        
        function submitSupplement() {
            NotificationManager.success('补药申请已提交，等待审批');
            setTimeout(() => window.location.href = 'step5.html', 2000);
        }
        
        function skipSupplement() {
            if (confirm('确认跳过补药操作？')) {
                workflowManager.completeStep(4, { status: 'skipped' });
                window.location.href = 'step5.html';
            }
        }
        
        function goBack() {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>
