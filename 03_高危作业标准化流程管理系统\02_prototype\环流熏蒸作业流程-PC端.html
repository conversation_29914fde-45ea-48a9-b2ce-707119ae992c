<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环流熏蒸作业流程管理系统 - PC端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .header .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .workflow-nav {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        
        .workflow-steps::before {
            content: '';
            position: absolute;
            top: 25px;
            left: 50px;
            right: 50px;
            height: 2px;
            background: #e0e0e0;
            z-index: 1;
        }
        
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            z-index: 2;
            background: white;
            padding: 0 15px;
        }
        
        .step-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
            border: 3px solid #e0e0e0;
            background: white;
            color: #999;
        }
        
        .step.active .step-circle {
            background: #4CAF50;
            border-color: #4CAF50;
            color: white;
        }
        
        .step.completed .step-circle {
            background: #2196F3;
            border-color: #2196F3;
            color: white;
        }
        
        .step-title {
            font-size: 14px;
            text-align: center;
            max-width: 100px;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 350px;
            gap: 20px;
        }
        
        .form-panel {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .form-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 10px;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .info-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            color: #666;
            font-size: 14px;
        }
        
        .info-value {
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-approved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .monitor-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .monitor-card {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #4CAF50;
        }
        
        .monitor-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .monitor-label {
            font-size: 12px;
            color: #666;
        }
        
        .alert-high {
            border-left-color: #f44336;
        }
        
        .alert-high .monitor-value {
            color: #f44336;
        }
        
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 4px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        .file-upload:hover {
            border-color: #4CAF50;
        }
        
        .file-upload-text {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>高危作业标准化流程管理系统</h1>
        <div class="subtitle">环流熏蒸作业流程 - 当前用户：张三（作业负责人）</div>
    </div>
    
    <div class="container">
        <!-- 流程导航 -->
        <div class="workflow-nav">
            <div class="workflow-steps">
                <div class="step completed">
                    <div class="step-circle">1</div>
                    <div class="step-title">熏蒸方案制定</div>
                </div>
                <div class="step completed">
                    <div class="step-circle">2</div>
                    <div class="step-title">作业前准备</div>
                </div>
                <div class="step active">
                    <div class="step-circle">3</div>
                    <div class="step-title">施药操作</div>
                </div>
                <div class="step">
                    <div class="step-circle">4</div>
                    <div class="step-title">补药操作</div>
                </div>
                <div class="step">
                    <div class="step-circle">5</div>
                    <div class="step-title">散气操作</div>
                </div>
                <div class="step">
                    <div class="step-circle">6</div>
                    <div class="step-title">效果检查</div>
                </div>
                <div class="step">
                    <div class="step-circle">7</div>
                    <div class="step-title">作业总结</div>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <!-- 主表单区域 -->
            <div class="form-panel">
                <div class="form-title">施药操作记录表</div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>作业申请编号</label>
                        <input type="text" value="FUM-2024-001" readonly>
                    </div>
                    <div class="form-group">
                        <label>仓房编号</label>
                        <input type="text" value="1号仓" readonly>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>粮食品种</label>
                        <input type="text" value="小麦" readonly>
                    </div>
                    <div class="form-group">
                        <label>粮食数量（吨）</label>
                        <input type="text" value="500" readonly>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>熏蒸剂类型</label>
                        <select>
                            <option value="磷化铝">磷化铝</option>
                            <option value="磷化镁">磷化镁</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>计划用量（kg）</label>
                        <input type="number" value="15.5" step="0.1">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>实际用量（kg）</label>
                        <input type="number" placeholder="请输入实际用量" step="0.1">
                    </div>
                    <div class="form-group">
                        <label>施药时间</label>
                        <input type="datetime-local">
                    </div>
                </div>
                
                <div class="form-group">
                    <label>施药方式</label>
                    <select>
                        <option value="环流施药">环流施药</option>
                        <option value="直接投药">直接投药</option>
                        <option value="管道施药">管道施药</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>施药位置描述</label>
                    <textarea rows="3" placeholder="请详细描述施药位置和分布情况"></textarea>
                </div>
                
                <div class="form-group">
                    <label>现场照片上传</label>
                    <div class="file-upload">
                        <div class="file-upload-text">点击或拖拽文件到此处上传</div>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label>操作人员</label>
                        <input type="text" placeholder="请输入操作人员姓名">
                    </div>
                    <div class="form-group">
                        <label>监督人员</label>
                        <input type="text" placeholder="请输入监督人员姓名">
                    </div>
                </div>
                
                <div class="form-group">
                    <label>备注说明</label>
                    <textarea rows="3" placeholder="请输入其他需要说明的情况"></textarea>
                </div>
                
                <div style="margin-top: 30px;">
                    <button class="btn btn-primary">保存记录</button>
                    <button class="btn btn-primary">提交审核</button>
                    <button class="btn btn-secondary">暂存草稿</button>
                </div>
            </div>
            
            <!-- 信息面板 -->
            <div>
                <div class="info-panel">
                    <div class="info-title">作业基本信息</div>
                    <div class="info-item">
                        <span class="info-label">申请编号</span>
                        <span class="info-value">FUM-2024-001</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">申请人</span>
                        <span class="info-value">张三</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">申请时间</span>
                        <span class="info-value">2024-01-15 09:30</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">审批状态</span>
                        <span class="info-value">
                            <span class="status-badge status-approved">已审批</span>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">风险等级</span>
                        <span class="info-value">高风险</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">计划开始</span>
                        <span class="info-value">2024-01-16 08:00</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">计划结束</span>
                        <span class="info-value">2024-01-23 18:00</span>
                    </div>
                </div>
                
                <!-- 实时监测数据 -->
                <div class="monitor-panel">
                    <div class="info-title">实时监测数据</div>
                    <div class="monitor-grid">
                        <div class="monitor-card">
                            <div class="monitor-value">23.5°C</div>
                            <div class="monitor-label">仓内温度</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-value">65%</div>
                            <div class="monitor-label">相对湿度</div>
                        </div>
                        <div class="monitor-card alert-high">
                            <div class="monitor-value">850</div>
                            <div class="monitor-label">PH3浓度(mg/m³)</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-value">18.5%</div>
                            <div class="monitor-label">氧气浓度</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-value">2.1%</div>
                            <div class="monitor-label">CO2浓度</div>
                        </div>
                        <div class="monitor-card">
                            <div class="monitor-value">正常</div>
                            <div class="monitor-label">设备状态</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px; font-size: 12px; color: #666;">
                        最后更新时间：2024-01-16 14:30:25
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 文件上传区域点击事件
            const fileUpload = document.querySelector('.file-upload');
            fileUpload.addEventListener('click', function() {
                const input = document.createElement('input');
                input.type = 'file';
                input.multiple = true;
                input.accept = 'image/*';
                input.click();

                input.addEventListener('change', function() {
                    if (input.files.length > 0) {
                        fileUpload.innerHTML = `<div class="file-upload-text">已选择 ${input.files.length} 个文件</div>`;
                    }
                });
            });

            // 按钮点击事件
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    const action = this.textContent;
                    alert(`执行操作：${action}`);
                });
            });

            // 模拟实时数据更新
            setInterval(function() {
                const tempValue = document.querySelector('.monitor-card:nth-child(1) .monitor-value');
                const currentTemp = parseFloat(tempValue.textContent);
                const newTemp = (currentTemp + (Math.random() - 0.5) * 0.2).toFixed(1);
                tempValue.textContent = newTemp + '°C';

                const ph3Value = document.querySelector('.monitor-card:nth-child(3) .monitor-value');
                const currentPh3 = parseInt(ph3Value.textContent);
                const newPh3 = Math.max(800, currentPh3 + Math.floor((Math.random() - 0.5) * 20));
                ph3Value.textContent = newPh3;

                // 更新时间
                const timeElement = document.querySelector('.monitor-panel div:last-child');
                const now = new Date();
                timeElement.textContent = `最后更新时间：${now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')} ${String(now.getHours()).padStart(2,'0')}:${String(now.getMinutes()).padStart(2,'0')}:${String(now.getSeconds()).padStart(2,'0')}`;
            }, 5000);
        });
    </script>
</body>
</html>
