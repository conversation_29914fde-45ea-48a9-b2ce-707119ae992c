/* 高危作业标准化流程管理系统 - 公共样式 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    font-size: 14px;
}

/* 容器样式 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.container-fluid {
    width: 100%;
    padding: 0 15px;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    font-size: 24px;
    font-weight: 600;
}

.header-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin-top: 4px;
}

.header-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    text-align: right;
}

.user-name {
    font-weight: 500;
    font-size: 16px;
}

.user-role {
    font-size: 12px;
    opacity: 0.8;
}

/* 导航样式 */
.nav-tabs {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.nav-item {
    padding: 12px 20px;
    cursor: pointer;
    border-radius: 6px 6px 0 0;
    transition: all 0.3s;
    position: relative;
}

.nav-item:hover {
    background: #f8f9fa;
}

.nav-item.active {
    background: #4CAF50;
    color: white;
}

.nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: #4CAF50;
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    transition: all 0.3s;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.card-subtitle {
    font-size: 14px;
    color: #666;
    margin-top: 4px;
}

.card-body {
    padding: 0;
}

.card-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
    font-size: 14px;
}

.form-label.required::after {
    content: '*';
    color: #f44336;
    margin-left: 4px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-control:read-only {
    background: #f8f9fa;
    color: #666;
}

.form-control.error {
    border-color: #f44336;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-error {
    color: #f44336;
    font-size: 12px;
    margin-top: 4px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    transition: all 0.3s;
    line-height: 1.4;
    min-width: 80px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-outline {
    background: transparent;
    border: 1px solid #4CAF50;
    color: #4CAF50;
}

.btn-outline:hover {
    background: #4CAF50;
    color: white;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
}

/* 状态标签 */
.badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.badge-success {
    background: #d4edda;
    color: #155724;
}

.badge-warning {
    background: #fff3cd;
    color: #856404;
}

.badge-danger {
    background: #f8d7da;
    color: #721c24;
}

.badge-info {
    background: #d1ecf1;
    color: #0c5460;
}

.badge-secondary {
    background: #e2e3e5;
    color: #383d41;
}

/* 进度条样式 */
.progress {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.6s ease;
    border-radius: 4px;
}

/* 工作流程样式 */
.workflow-container {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.workflow-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    margin: 20px 0;
}

.workflow-steps::before {
    content: '';
    position: absolute;
    top: 30px;
    left: 60px;
    right: 60px;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
}

.workflow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: white;
    padding: 0 20px;
    cursor: pointer;
    transition: all 0.3s;
}

.workflow-step:hover {
    transform: scale(1.05);
}

.step-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 12px;
    border: 3px solid #e0e0e0;
    background: white;
    color: #999;
    transition: all 0.3s;
}

.workflow-step.completed .step-circle {
    background: #4CAF50;
    border-color: #4CAF50;
    color: white;
}

.workflow-step.active .step-circle {
    background: #2196F3;
    border-color: #2196F3;
    color: white;
    animation: pulse 2s infinite;
}

.workflow-step.error .step-circle {
    background: #f44336;
    border-color: #f44336;
    color: white;
}

.step-title {
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    max-width: 120px;
    color: #333;
}

.step-time {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

/* 动画效果 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(33, 150, 243, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .workflow-steps {
        flex-direction: column;
        gap: 20px;
    }
    
    .workflow-steps::before {
        display: none;
    }
    
    .workflow-step {
        flex-direction: row;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        width: 100%;
        justify-content: flex-start;
        gap: 15px;
    }
    
    .step-circle {
        width: 40px;
        height: 40px;
        font-size: 14px;
        margin-bottom: 0;
    }
    
    .step-title {
        text-align: left;
        max-width: none;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 0;
    }
    
    .nav-item {
        border-radius: 0;
        border-bottom: 1px solid #e0e0e0;
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }

.p-10 { padding: 10px; }
.p-20 { padding: 20px; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.text-success { color: #28a745; }
.text-warning { color: #ffc107; }
.text-danger { color: #dc3545; }
.text-info { color: #17a2b8; }
.text-muted { color: #6c757d; }
