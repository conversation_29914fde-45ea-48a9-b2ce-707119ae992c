-- 安全生产作业流程系统数据库设计
-- 扩展现有风险监测系统，增加作业流程管理功能

USE grain_risk_monitor;

-- 1. 组织机构表（支持多租户）
CREATE TABLE organization (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    org_code VARCHAR(50) NOT NULL COMMENT '机构编码',
    org_name VARCHAR(100) NOT NULL COMMENT '机构名称',
    parent_id BIGINT COMMENT '父机构ID',
    org_type TINYINT NOT NULL COMMENT '机构类型：1-总部，2-省级，3-市级，4-库点',
    org_level INT NOT NULL DEFAULT 1 COMMENT '机构层级',
    org_path VARCHAR(500) COMMENT '机构路径',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    address VARCHAR(200) COMMENT '地址',
    is_active TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    is_deleted TINYINT NOT NULL DEFAULT 0,
    
    UNIQUE KEY uk_org_code (org_code),
    KEY idx_parent_id (parent_id),
    KEY idx_org_type (org_type),
    KEY idx_org_level (org_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织机构表';

-- 2. 作业类型配置表
CREATE TABLE operation_type (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    type_code VARCHAR(50) NOT NULL COMMENT '作业类型编码',
    type_name VARCHAR(100) NOT NULL COMMENT '作业类型名称',
    risk_level TINYINT NOT NULL DEFAULT 1 COMMENT '风险等级：1-低风险，2-中风险，3-高风险',
    description TEXT COMMENT '作业描述',
    safety_requirements TEXT COMMENT '安全要求',
    is_active TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    is_deleted TINYINT NOT NULL DEFAULT 0,
    
    UNIQUE KEY uk_type_code (type_code),
    KEY idx_risk_level (risk_level),
    KEY idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='作业类型配置表';

-- 3. 流程模板表（三层模板体系）
CREATE TABLE process_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    template_code VARCHAR(50) NOT NULL COMMENT '模板编码',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    operation_type_code VARCHAR(50) NOT NULL COMMENT '作业类型编码',
    template_level TINYINT NOT NULL COMMENT '模板层级：1-国家标准，2-区域定制，3-库点执行',
    parent_template_id BIGINT COMMENT '父模板ID',
    org_id BIGINT COMMENT '适用机构ID（区域/库点模板）',
    template_content JSON NOT NULL COMMENT '模板内容（流程定义）',
    version VARCHAR(20) NOT NULL DEFAULT '1.0' COMMENT '版本号',
    is_current TINYINT NOT NULL DEFAULT 1 COMMENT '是否当前版本',
    effective_date DATE COMMENT '生效日期',
    expire_date DATE COMMENT '失效日期',
    approval_status TINYINT NOT NULL DEFAULT 1 COMMENT '审批状态：1-草稿，2-待审批，3-已审批，4-已拒绝',
    is_active TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    is_deleted TINYINT NOT NULL DEFAULT 0,
    
    UNIQUE KEY uk_template_code_version (template_code, version),
    KEY idx_operation_type (operation_type_code),
    KEY idx_template_level (template_level),
    KEY idx_parent_template (parent_template_id),
    KEY idx_org_id (org_id),
    KEY idx_is_current (is_current)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流程模板表';

-- 4. 表单模板表
CREATE TABLE form_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    template_code VARCHAR(50) NOT NULL COMMENT '表单模板编码',
    template_name VARCHAR(100) NOT NULL COMMENT '表单模板名称',
    operation_type_code VARCHAR(50) NOT NULL COMMENT '作业类型编码',
    form_type TINYINT NOT NULL COMMENT '表单类型：1-申请表，2-检查表，3-验收表',
    template_level TINYINT NOT NULL COMMENT '模板层级：1-国家标准，2-区域定制，3-库点执行',
    org_id BIGINT COMMENT '适用机构ID',
    form_fields JSON NOT NULL COMMENT '表单字段定义',
    validation_rules JSON COMMENT '验证规则',
    version VARCHAR(20) NOT NULL DEFAULT '1.0' COMMENT '版本号',
    is_current TINYINT NOT NULL DEFAULT 1 COMMENT '是否当前版本',
    is_active TINYINT NOT NULL DEFAULT 1 COMMENT '是否启用',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    is_deleted TINYINT NOT NULL DEFAULT 0,
    
    UNIQUE KEY uk_form_template_code_version (template_code, version),
    KEY idx_operation_type (operation_type_code),
    KEY idx_form_type (form_type),
    KEY idx_template_level (template_level),
    KEY idx_org_id (org_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单模板表';

-- 5. 作业申请表
CREATE TABLE operation_application (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    application_no VARCHAR(50) NOT NULL COMMENT '申请编号',
    operation_type_code VARCHAR(50) NOT NULL COMMENT '作业类型编码',
    org_id BIGINT NOT NULL COMMENT '申请机构ID',
    applicant_id BIGINT NOT NULL COMMENT '申请人ID',
    applicant_name VARCHAR(50) NOT NULL COMMENT '申请人姓名',
    operation_title VARCHAR(200) NOT NULL COMMENT '作业标题',
    operation_location VARCHAR(200) NOT NULL COMMENT '作业地点',
    planned_start_time DATETIME NOT NULL COMMENT '计划开始时间',
    planned_end_time DATETIME NOT NULL COMMENT '计划结束时间',
    operation_content TEXT COMMENT '作业内容',
    risk_analysis TEXT COMMENT '风险分析',
    safety_measures TEXT COMMENT '安全措施',
    application_data JSON COMMENT '申请表单数据',
    current_status TINYINT NOT NULL DEFAULT 1 COMMENT '当前状态：1-草稿，2-待审批，3-已审批，4-执行中，5-已完成，6-已取消',
    process_instance_id VARCHAR(100) COMMENT '流程实例ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by VARCHAR(50),
    updated_by VARCHAR(50),
    is_deleted TINYINT NOT NULL DEFAULT 0,
    
    UNIQUE KEY uk_application_no (application_no),
    KEY idx_operation_type (operation_type_code),
    KEY idx_org_id (org_id),
    KEY idx_applicant_id (applicant_id),
    KEY idx_current_status (current_status),
    KEY idx_planned_start_time (planned_start_time),
    KEY idx_process_instance_id (process_instance_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='作业申请表';

-- 6. 流程实例表
CREATE TABLE process_instance (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    instance_id VARCHAR(100) NOT NULL COMMENT '流程实例ID',
    template_id BIGINT NOT NULL COMMENT '流程模板ID',
    application_id BIGINT NOT NULL COMMENT '作业申请ID',
    current_node VARCHAR(100) COMMENT '当前节点',
    instance_status TINYINT NOT NULL DEFAULT 1 COMMENT '实例状态：1-运行中，2-已完成，3-已终止',
    start_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    instance_data JSON COMMENT '实例数据',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_instance_id (instance_id),
    KEY idx_template_id (template_id),
    KEY idx_application_id (application_id),
    KEY idx_instance_status (instance_status),
    KEY idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流程实例表';

-- 7. 流程任务表
CREATE TABLE process_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    task_id VARCHAR(100) NOT NULL COMMENT '任务ID',
    instance_id VARCHAR(100) NOT NULL COMMENT '流程实例ID',
    node_id VARCHAR(100) NOT NULL COMMENT '节点ID',
    node_name VARCHAR(100) NOT NULL COMMENT '节点名称',
    task_type TINYINT NOT NULL COMMENT '任务类型：1-审批，2-执行，3-检查，4-验收',
    assignee_id BIGINT COMMENT '处理人ID',
    assignee_name VARCHAR(50) COMMENT '处理人姓名',
    task_status TINYINT NOT NULL DEFAULT 1 COMMENT '任务状态：1-待处理，2-处理中，3-已完成，4-已拒绝',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '完成时间',
    task_data JSON COMMENT '任务数据',
    comments TEXT COMMENT '处理意见',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_task_id (task_id),
    KEY idx_instance_id (instance_id),
    KEY idx_node_id (node_id),
    KEY idx_assignee_id (assignee_id),
    KEY idx_task_status (task_status),
    KEY idx_start_time (start_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流程任务表';
