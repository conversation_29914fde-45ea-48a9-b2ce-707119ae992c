<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑NFC标签 - 中科抗菌管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            color: white;
        }
        
        .main-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .form-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }
        
        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .form-header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-header h1 {
            color: #2c3e50;
            font-size: 24px;
        }
        
        .tag-status {
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .tag-status.active {
            background: #d4edda;
            color: #155724;
        }
        
        .tag-status.inactive {
            background: #f8d7da;
            color: #721c24;
        }
        
        .tag-status.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border-left: 4px solid #007bff;
        }
        
        .form-section.product {
            border-left-color: #28a745;
        }
        
        .form-section.certificate {
            border-left-color: #ffc107;
        }
        
        .form-section.system {
            border-left-color: #dc3545;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .form-label.required::after {
            content: ' *';
            color: #dc3545;
        }
        
        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .form-input:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }
        
        .form-textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .tag-input-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            min-height: 45px;
            cursor: text;
        }
        
        .tag-input-container:focus-within {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .tag-item {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .tag-remove {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 14px;
            padding: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .tag-remove:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .tag-input {
            border: none;
            outline: none;
            flex: 1;
            min-width: 100px;
            padding: 4px;
            font-size: 14px;
        }
        
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }
        
        .checkbox-item label {
            font-size: 14px;
            color: #2c3e50;
            cursor: pointer;
        }
        
        .form-actions {
            grid-column: 1 / -1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #e9ecef;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 2px solid #6c757d;
        }
        
        .btn-outline:hover {
            background: #6c757d;
            color: white;
        }
        
        .help-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
            line-height: 1.4;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .alert-danger {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        
        .back-button {
            margin-bottom: 20px;
        }
        
        .change-log {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .change-log h3 {
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .change-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #bbdefb;
        }
        
        .change-item:last-child {
            border-bottom: none;
        }
        
        .change-time {
            font-size: 12px;
            color: #1976d2;
            white-space: nowrap;
        }
        
        .change-desc {
            flex: 1;
            font-size: 14px;
            color: #2c3e50;
        }
        
        .change-user {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="breadcrumb">
                <a href="管理端主界面.html">🏠 首页</a>
                <span>></span>
                <a href="管理端主界面.html">NFC标签管理</a>
                <span>></span>
                <span>编辑标签</span>
            </div>
            <div>
                <button class="btn btn-outline" onclick="window.close()">关闭</button>
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <div class="back-button">
            <button class="btn btn-outline" onclick="window.history.back()">
                ← 返回列表
            </button>
        </div>
        
        <div class="form-container">
            <div class="form-header">
                <div class="form-header-left">
                    <span style="font-size: 32px;">✏️</span>
                    <div>
                        <h1>编辑NFC标签</h1>
                        <div style="font-size: 14px; color: #6c757d; margin-top: 5px;">
                            标签ID: <span id="currentTagId">NFC-ZK-20240315-001</span>
                        </div>
                    </div>
                </div>
                <div>
                    <span class="tag-status active" id="tagStatusBadge">有效</span>
                </div>
            </div>
            
            <div class="alert alert-info">
                <strong>📝 编辑说明</strong><br>
                修改标签信息后，所有变更将被记录。带 * 号的为必填项。
            </div>
            
            <form id="tagForm" class="form-grid">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h2 class="section-title">
                        🏷️ 基本信息
                    </h2>

                    <div class="form-group">
                        <label class="form-label">标签ID</label>
                        <input type="text" class="form-input" id="tagId" readonly
                               value="NFC-ZK-20240315-001"
                               style="background: #f8f9fa; color: #6c757d;">
                        <div class="help-text">标签ID不可修改</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">标签状态</label>
                        <select class="form-select" id="tagStatus" required>
                            <option value="active" selected>有效</option>
                            <option value="inactive">无效</option>
                            <option value="pending">待审核</option>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">创建日期</label>
                            <input type="date" class="form-input" id="createDate"
                                   value="2024-03-15" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">有效期限</label>
                            <input type="date" class="form-input" id="expireDate"
                                   value="2027-03-15" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">备注说明</label>
                        <textarea class="form-textarea" id="tagNotes"
                                  placeholder="请输入标签备注信息...">正版中科抗菌纺织品，具有优异的抗菌性能。</textarea>
                    </div>
                </div>

                <!-- 产品信息 -->
                <div class="form-section product">
                    <h2 class="section-title">
                        📦 产品信息
                    </h2>

                    <div class="form-group">
                        <label class="form-label required">产品名称</label>
                        <input type="text" class="form-input" id="productName" required
                               value="中科抗菌纺织品">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">产品型号</label>
                            <input type="text" class="form-input" id="productModel" required
                                   value="ZK-AB-2024">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">生产批次</label>
                            <input type="text" class="form-input" id="productBatch" required
                                   value="20240315001">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">抗菌等级</label>
                            <select class="form-select" id="antibacterialLevel" required>
                                <option value="AAA" selected>AAA级</option>
                                <option value="AA">AA级</option>
                                <option value="A">A级</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">生产日期</label>
                            <input type="date" class="form-input" id="productionDate"
                                   value="2024-03-15" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">产品描述</label>
                        <textarea class="form-textarea" id="productDescription">采用中科院最新抗菌技术，具有持久抗菌效果，抗菌率达99.9%以上。适用于医疗、家居等多种场景。</textarea>
                    </div>
                </div>

                <!-- 认证信息 -->
                <div class="form-section certificate">
                    <h2 class="section-title">
                        🏆 认证信息
                    </h2>

                    <div class="form-group">
                        <label class="form-label required">专利证书编号</label>
                        <input type="text" class="form-input" id="patentNumber" required
                               value="ZL202410123456.7">
                    </div>

                    <div class="form-group">
                        <label class="form-label">获奖情况</label>
                        <div class="tag-input-container" onclick="focusTagInput()">
                            <div id="awardTags">
                                <div class="tag-item">
                                    <span>国家发明专利证书</span>
                                    <button type="button" class="tag-remove" onclick="removeTag(this)">×</button>
                                </div>
                                <div class="tag-item">
                                    <span>中国纺织工业联合会科技进步一等奖</span>
                                    <button type="button" class="tag-remove" onclick="removeTag(this)">×</button>
                                </div>
                            </div>
                            <input type="text" class="tag-input" id="awardInput"
                                   placeholder="输入获奖名称后按回车添加"
                                   onkeypress="handleTagInput(event, 'award')">
                        </div>
                        <div class="help-text">按回车键添加获奖项目，可添加多个</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">检测报告编号</label>
                        <input type="text" class="form-input" id="testReportNumber" required
                               value="ISO20743-2024-001">
                    </div>

                    <div class="form-group">
                        <label class="form-label">认证机构</label>
                        <input type="text" class="form-input" id="certificationOrg"
                               value="中国纺织工业联合会">
                    </div>

                    <div class="form-group">
                        <label class="form-label">技术参数</label>
                        <div class="tag-input-container" onclick="focusTagInput()">
                            <div id="paramTags">
                                <div class="tag-item">
                                    <span>抗菌率>99.9%</span>
                                    <button type="button" class="tag-remove" onclick="removeTag(this)">×</button>
                                </div>
                                <div class="tag-item">
                                    <span>银离子抗菌技术</span>
                                    <button type="button" class="tag-remove" onclick="removeTag(this)">×</button>
                                </div>
                            </div>
                            <input type="text" class="tag-input" id="paramInput"
                                   placeholder="输入技术参数后按回车添加"
                                   onkeypress="handleTagInput(event, 'param')">
                        </div>
                        <div class="help-text">如：抗菌率>99%、材料成分等</div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div class="form-section system">
                    <h2 class="section-title">
                        ⚙️ 系统设置
                    </h2>

                    <div class="form-group">
                        <label class="form-label">验证权限</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="allowPublicVerify" checked>
                                <label for="allowPublicVerify">允许公开验证</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="allowOfflineVerify">
                                <label for="allowOfflineVerify">支持离线验证</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="requireLocation" checked>
                                <label for="requireLocation">验证时记录位置</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableNotification" checked>
                                <label for="enableNotification">启用验证通知</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">验证限制</label>
                            <select class="form-select" id="verifyLimit">
                                <option value="unlimited" selected>无限制</option>
                                <option value="daily">每日限制</option>
                                <option value="total">总次数限制</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">限制次数</label>
                            <input type="number" class="form-input" id="limitCount"
                                   value="0" min="0">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">关联视频URL</label>
                        <input type="url" class="form-input" id="videoUrl"
                               value="https://example.com/zkab_awards.mp4">
                        <div class="help-text">专利获奖展示视频链接</div>
                    </div>
                </div>

                <!-- 变更记录 -->
                <div class="form-section full-width">
                    <div class="change-log">
                        <h3>
                            📝 变更记录
                        </h3>
                        <div class="change-item">
                            <div class="change-time">2024-03-25<br>10:15</div>
                            <div class="change-desc">修改了产品描述信息</div>
                            <div class="change-user">张三</div>
                        </div>
                        <div class="change-item">
                            <div class="change-time">2024-03-20<br>14:30</div>
                            <div class="change-desc">更新了专利证书编号</div>
                            <div class="change-user">李四</div>
                        </div>
                        <div class="change-item">
                            <div class="change-time">2024-03-15<br>09:00</div>
                            <div class="change-desc">创建了NFC标签</div>
                            <div class="change-user">系统</div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <div>
                        <button type="button" class="btn btn-outline" onclick="resetForm()">
                            🔄 撤销更改
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="saveAsDraft()">
                            💾 保存草稿
                        </button>
                        <button type="button" class="btn btn-danger" onclick="disableTag()">
                            🚫 禁用标签
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="previewTag()">
                            👀 预览效果
                        </button>
                        <button type="submit" class="btn btn-success">
                            ✅ 保存更改
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        window.addEventListener('load', function() {
            // 从URL获取标签ID
            const urlParams = new URLSearchParams(window.location.search);
            const tagId = urlParams.get('id');

            if (tagId) {
                loadTagData(tagId);
            }

            // 绑定表单变化事件
            bindFormEvents();
        });

        // 加载标签数据
        function loadTagData(tagId) {
            // 模拟从API加载数据
            const tagData = {
                tagId: tagId,
                status: 'active',
                productName: '中科抗菌纺织品',
                productModel: 'ZK-AB-2024',
                productBatch: '20240315001',
                antibacterialLevel: 'AAA',
                productionDate: '2024-03-15',
                patentNumber: 'ZL202410123456.7',
                testReportNumber: 'ISO20743-2024-001',
                certificationOrg: '中国纺织工业联合会',
                videoUrl: 'https://example.com/zkab_awards.mp4',
                createDate: '2024-03-15',
                expireDate: '2027-03-15',
                tagNotes: '正版中科抗菌纺织品，具有优异的抗菌性能。',
                productDescription: '采用中科院最新抗菌技术，具有持久抗菌效果，抗菌率达99.9%以上。适用于医疗、家居等多种场景。'
            };

            // 填充表单数据
            document.getElementById('currentTagId').textContent = tagData.tagId;
            document.getElementById('tagId').value = tagData.tagId;
            document.getElementById('tagStatus').value = tagData.status;
            document.getElementById('productName').value = tagData.productName;
            document.getElementById('productModel').value = tagData.productModel;
            document.getElementById('productBatch').value = tagData.productBatch;
            document.getElementById('antibacterialLevel').value = tagData.antibacterialLevel;
            document.getElementById('productionDate').value = tagData.productionDate;
            document.getElementById('patentNumber').value = tagData.patentNumber;
            document.getElementById('testReportNumber').value = tagData.testReportNumber;
            document.getElementById('certificationOrg').value = tagData.certificationOrg;
            document.getElementById('videoUrl').value = tagData.videoUrl;
            document.getElementById('createDate').value = tagData.createDate;
            document.getElementById('expireDate').value = tagData.expireDate;
            document.getElementById('tagNotes').value = tagData.tagNotes;
            document.getElementById('productDescription').value = tagData.productDescription;

            // 更新状态徽章
            updateStatusBadge(tagData.status);
        }

        // 更新状态徽章
        function updateStatusBadge(status) {
            const badge = document.getElementById('tagStatusBadge');
            badge.className = `tag-status ${status}`;

            switch(status) {
                case 'active':
                    badge.textContent = '有效';
                    break;
                case 'inactive':
                    badge.textContent = '无效';
                    break;
                case 'pending':
                    badge.textContent = '待审核';
                    break;
            }
        }

        // 绑定表单事件
        function bindFormEvents() {
            // 状态变化时更新徽章
            document.getElementById('tagStatus').addEventListener('change', function() {
                updateStatusBadge(this.value);
            });

            // 绑定其他表单变化事件
            const form = document.getElementById('tagForm');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.addEventListener('input', markAsChanged);
                input.addEventListener('change', markAsChanged);
            });
        }

        // 标记表单已更改
        function markAsChanged() {
            // 可以在这里添加表单变更标记
            console.log('表单已更改');
        }

        // 处理标签输入
        function handleTagInput(event, type) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const input = event.target;
                const value = input.value.trim();

                if (value) {
                    addTag(type, value);
                    input.value = '';
                }
            }
        }

        // 添加标签
        function addTag(type, value) {
            const container = document.getElementById(type + 'Tags');
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `
                <span>${value}</span>
                <button type="button" class="tag-remove" onclick="removeTag(this)">×</button>
            `;
            container.appendChild(tagElement);
        }

        // 移除标签
        function removeTag(button) {
            button.parentElement.remove();
        }

        // 聚焦标签输入
        function focusTagInput() {
            const activeInput = document.querySelector('.tag-input:focus');
            if (!activeInput) {
                document.querySelector('.tag-input').focus();
            }
        }

        // 预览标签
        function previewTag() {
            const formData = getFormData();

            // 打开预览窗口
            const previewUrl = `APP端验证页面.html?preview=true&data=${encodeURIComponent(JSON.stringify(formData))}`;
            window.open(previewUrl, '_blank');
        }

        // 获取表单数据
        function getFormData() {
            return {
                tagId: document.getElementById('tagId').value,
                productName: document.getElementById('productName').value,
                productModel: document.getElementById('productModel').value,
                productBatch: document.getElementById('productBatch').value,
                antibacterialLevel: document.getElementById('antibacterialLevel').value,
                patentNumber: document.getElementById('patentNumber').value,
                status: document.getElementById('tagStatus').value
            };
        }

        // 撤销更改
        function resetForm() {
            if (confirm('确定要撤销所有更改吗？')) {
                const urlParams = new URLSearchParams(window.location.search);
                const tagId = urlParams.get('id');
                if (tagId) {
                    loadTagData(tagId);
                }
            }
        }

        // 保存草稿
        function saveAsDraft() {
            const formData = getFormData();
            localStorage.setItem(`nfc_tag_draft_${formData.tagId}`, JSON.stringify(formData));
            alert('草稿已保存！');
        }

        // 禁用标签
        function disableTag() {
            if (confirm('确定要禁用此标签吗？禁用后用户将无法通过验证。')) {
                document.getElementById('tagStatus').value = 'inactive';
                updateStatusBadge('inactive');
                alert('标签已禁用');
            }
        }

        // 表单提交
        document.getElementById('tagForm').addEventListener('submit', function(event) {
            event.preventDefault();

            if (!validateForm()) {
                return;
            }

            const formData = getFormData();

            // 模拟提交
            if (confirm('确定要保存对此NFC标签的更改吗？')) {
                // 这里应该调用API提交数据
                alert('NFC标签更新成功！\n\n标签ID：' + formData.tagId + '\n\n更改已保存。');

                // 跳转到详情页面
                window.location.href = `标签详情页面.html?id=${formData.tagId}`;
            }
        });

        // 表单验证
        function validateForm() {
            const requiredFields = [
                'productName', 'productModel', 'productBatch',
                'antibacterialLevel', 'productionDate', 'patentNumber', 'testReportNumber'
            ];

            for (let field of requiredFields) {
                const element = document.getElementById(field);
                if (!element.value.trim()) {
                    alert(`请填写${element.previousElementSibling.textContent.replace(' *', '')}`);
                    element.focus();
                    return false;
                }
            }

            return true;
        }
    </script>
</body>
</html>
