<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中科抗菌NFC标签管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            border-left: 4px solid #007bff;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-card.success {
            border-left-color: #28a745;
        }
        
        .stat-card.warning {
            border-left-color: #ffc107;
        }
        
        .stat-card.danger {
            border-left-color: #dc3545;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .stat-title {
            font-size: 14px;
            color: #6c757d;
            font-weight: 500;
        }
        
        .stat-icon {
            font-size: 24px;
            opacity: 0.7;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-change {
            font-size: 12px;
            color: #28a745;
        }
        
        .stat-change.negative {
            color: #dc3545;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 1px solid #ddd;
        }
        
        .btn-outline:hover {
            background: #f8f9fa;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .table td {
            font-size: 14px;
            color: #495057;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-badge.valid {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge.invalid {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-badge.pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
            color: #6c757d;
        }
        
        .recent-activity {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
            flex-shrink: 0;
        }
        
        .activity-icon.success {
            background: #28a745;
        }
        
        .activity-icon.danger {
            background: #dc3545;
        }
        
        .activity-icon.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 4px;
        }
        
        .activity-desc {
            font-size: 12px;
            color: #6c757d;
        }
        
        .activity-time {
            font-size: 12px;
            color: #6c757d;
            white-space: nowrap;
        }
        
        .search-bar {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .filter-select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .pagination button:hover {
            background: #f8f9fa;
        }
        
        .pagination button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="logo">🛡️ 中科抗菌NFC标签管理系统</div>
            <div class="user-info">
                <span>管理员：张三</span>
                <button class="btn btn-outline">退出登录</button>
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card success">
                <div class="stat-header">
                    <span class="stat-title">总标签数量</span>
                    <span class="stat-icon">🏷️</span>
                </div>
                <div class="stat-value">12,456</div>
                <div class="stat-change">+156 本月新增</div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-header">
                    <span class="stat-title">有效标签</span>
                    <span class="stat-icon">✅</span>
                </div>
                <div class="stat-value">11,892</div>
                <div class="stat-change">95.5% 有效率</div>
            </div>
            
            <div class="stat-card danger">
                <div class="stat-header">
                    <span class="stat-title">异常标签</span>
                    <span class="stat-icon">❌</span>
                </div>
                <div class="stat-value">564</div>
                <div class="stat-change negative">+23 需要处理</div>
            </div>
            
            <div class="stat-card warning">
                <div class="stat-header">
                    <span class="stat-title">今日验证</span>
                    <span class="stat-icon">🔍</span>
                </div>
                <div class="stat-value">1,234</div>
                <div class="stat-change">+12% 较昨日</div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="content-grid">
            <!-- 标签管理表格 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        🏷️ NFC标签管理
                    </h2>
                    <div>
                        <button class="btn btn-success" onclick="addNewTag()">
                            ➕ 新增标签
                        </button>
                        <button class="btn btn-outline" onclick="exportData()">
                            📤 导出数据
                        </button>
                    </div>
                </div>
                
                <!-- 搜索和筛选 -->
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="搜索标签ID、产品名称...">
                    <select class="filter-select">
                        <option value="">全部状态</option>
                        <option value="valid">有效</option>
                        <option value="invalid">无效</option>
                        <option value="pending">待审核</option>
                    </select>
                    <button class="btn btn-primary">🔍 搜索</button>
                </div>
                
                <!-- 标签列表 -->
                <table class="table">
                    <thead>
                        <tr>
                            <th>标签ID</th>
                            <th>产品信息</th>
                            <th>状态</th>
                            <th>验证次数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>NFC-ZK-20240315-001</td>
                            <td>
                                <div>中科抗菌纺织品</div>
                                <small style="color: #6c757d;">ZK-AB-2024</small>
                            </td>
                            <td><span class="status-badge valid">有效</span></td>
                            <td>23</td>
                            <td>2024-03-15</td>
                            <td>
                                <button class="btn btn-outline" onclick="viewDetails('NFC-ZK-20240315-001')">查看</button>
                                <button class="btn btn-outline" onclick="editTag('NFC-ZK-20240315-001')">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>NFC-ZK-20240315-002</td>
                            <td>
                                <div>中科抗菌纺织品</div>
                                <small style="color: #6c757d;">ZK-AB-2024</small>
                            </td>
                            <td><span class="status-badge invalid">无效</span></td>
                            <td>5</td>
                            <td>2024-03-15</td>
                            <td>
                                <button class="btn btn-outline" onclick="viewDetails('NFC-ZK-20240315-002')">查看</button>
                                <button class="btn btn-outline" onclick="editTag('NFC-ZK-20240315-002')">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>NFC-ZK-20240316-001</td>
                            <td>
                                <div>中科抗菌床品套装</div>
                                <small style="color: #6c757d;">ZK-BED-2024</small>
                            </td>
                            <td><span class="status-badge pending">待审核</span></td>
                            <td>0</td>
                            <td>2024-03-16</td>
                            <td>
                                <button class="btn btn-outline" onclick="viewDetails('NFC-ZK-20240316-001')">查看</button>
                                <button class="btn btn-outline" onclick="editTag('NFC-ZK-20240316-001')">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>NFC-ZK-20240316-002</td>
                            <td>
                                <div>中科抗菌运动服</div>
                                <small style="color: #6c757d;">ZK-SPORT-2024</small>
                            </td>
                            <td><span class="status-badge valid">有效</span></td>
                            <td>12</td>
                            <td>2024-03-16</td>
                            <td>
                                <button class="btn btn-outline" onclick="viewDetails('NFC-ZK-20240316-002')">查看</button>
                                <button class="btn btn-outline" onclick="editTag('NFC-ZK-20240316-002')">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>NFC-ZK-20240317-001</td>
                            <td>
                                <div>中科抗菌口罩</div>
                                <small style="color: #6c757d;">ZK-MASK-2024</small>
                            </td>
                            <td><span class="status-badge valid">有效</span></td>
                            <td>45</td>
                            <td>2024-03-17</td>
                            <td>
                                <button class="btn btn-outline" onclick="viewDetails('NFC-ZK-20240317-001')">查看</button>
                                <button class="btn btn-outline" onclick="editTag('NFC-ZK-20240317-001')">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination">
                    <button disabled>上一页</button>
                    <button class="active">1</button>
                    <button>2</button>
                    <button>3</button>
                    <button>...</button>
                    <button>10</button>
                    <button>下一页</button>
                </div>
            </div>
            
            <!-- 右侧面板 -->
            <div>
                <!-- 验证统计图表 -->
                <div class="card" style="margin-bottom: 20px;">
                    <div class="card-header">
                        <h3 class="card-title">📊 验证统计</h3>
                        <select class="filter-select">
                            <option>最近7天</option>
                            <option>最近30天</option>
                            <option>最近90天</option>
                        </select>
                    </div>
                    <div class="chart-container">
                        📈 验证趋势图表<br>
                        <small>（此处可集成Chart.js等图表库）</small>
                    </div>
                </div>
                
                <!-- 最近活动 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🕒 最近活动</h3>
                        <button class="btn btn-outline">查看全部</button>
                    </div>
                    <div class="recent-activity">
                        <div class="activity-item">
                            <div class="activity-icon success">✅</div>
                            <div class="activity-content">
                                <div class="activity-title">标签验证成功</div>
                                <div class="activity-desc">NFC-ZK-20240315-001 在北京市朝阳区验证</div>
                            </div>
                            <div class="activity-time">2分钟前</div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon danger">❌</div>
                            <div class="activity-content">
                                <div class="activity-title">发现异常标签</div>
                                <div class="activity-desc">NFC-ZK-20240315-002 验证失败，疑似伪造</div>
                            </div>
                            <div class="activity-time">5分钟前</div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon success">➕</div>
                            <div class="activity-content">
                                <div class="activity-title">新增标签</div>
                                <div class="activity-desc">批量导入100个新标签</div>
                            </div>
                            <div class="activity-time">1小时前</div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon warning">⚠️</div>
                            <div class="activity-content">
                                <div class="activity-title">标签即将过期</div>
                                <div class="activity-desc">23个标签将在7天内过期</div>
                            </div>
                            <div class="activity-time">2小时前</div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon success">✅</div>
                            <div class="activity-content">
                                <div class="activity-title">批量验证完成</div>
                                <div class="activity-desc">今日验证1,234次，成功率98.5%</div>
                            </div>
                            <div class="activity-time">3小时前</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 新增标签
        function addNewTag() {
            window.open('新增标签页面.html', '_blank');
        }
        
        // 查看标签详情
        function viewDetails(tagId) {
            window.open(`标签详情页面.html?id=${tagId}`, '_blank');
        }
        
        // 编辑标签
        function editTag(tagId) {
            window.open(`编辑标签页面.html?id=${tagId}`, '_blank');
        }
        
        // 导出数据
        function exportData() {
            alert('正在导出数据...');
            // 实际应用中这里会调用导出API
        }
        
        // 模拟实时数据更新
        setInterval(function() {
            // 这里可以通过WebSocket或定时轮询更新数据
            console.log('更新实时数据');
        }, 30000);
    </script>
</body>
</html>
