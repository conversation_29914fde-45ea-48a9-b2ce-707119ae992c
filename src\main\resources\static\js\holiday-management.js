/**
 * 节假日管理系统 JavaScript
 */

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let datePicker = null;

// API基础路径
const API_BASE_URL = '/grain-risk/api/holidays';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 初始化日期选择器
    initDatePicker();

    // 加载节假日数据
    loadHolidays();

    // 初始化年份选择器
    initYearSelector();

    // 绑定事件监听器
    bindEventListeners();
}

/**
 * 初始化日期选择器
 */
function initDatePicker() {
    datePicker = flatpickr("#modalHolidayDate", {
        locale: "zh",
        dateFormat: "Y-m-d",
        allowInput: true,
        clickOpens: true
    });
}

/**
 * 初始化年份选择器
 */
function initYearSelector() {
    const currentYear = new Date().getFullYear();
    const yearSelects = ['#yearSelect', '#modalYearNum'];

    yearSelects.forEach(selector => {
        const select = document.querySelector(selector);
        if (select) {
            // 清空现有选项（保留第一个默认选项）
            const firstOption = select.firstElementChild;
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            // 添加年份选项（当前年份前后5年）
            for (let year = currentYear - 2; year <= currentYear + 5; year++) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + '年';
                if (year === currentYear) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        }
    });
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 搜索表单回车事件
    document.getElementById('holidayName').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchHolidays();
        }
    });

    // 节假日类型变化事件
    document.getElementById('modalHolidayType').addEventListener('change', function() {
        const isWorkdaySelect = document.getElementById('modalIsWorkday');
        if (this.value === '2') { // 调休
            isWorkdaySelect.value = '1'; // 默认为工作日
        } else {
            isWorkdaySelect.value = '0'; // 默认为休息日
        }
    });
}

/**
 * 加载节假日数据
 */
async function loadHolidays(page = 1) {
    try {
        showLoading();

        const params = new URLSearchParams({
            page: page,
            size: pageSize,
            year: document.getElementById('yearSelect').value || '',
            holidayType: document.getElementById('holidayTypeSelect').value || '',
            holidayName: document.getElementById('holidayName').value || ''
        });

        const response = await axios.get(`${API_BASE_URL}?${params}`);
        const data = response.data;

        if (data.success) {
            renderHolidayTable(data.data.records);
            renderPagination(data.data.current, data.data.pages, data.data.total);
            currentPage = data.data.current;
            totalPages = data.data.pages;
        } else {
            showError('加载数据失败：' + data.message);
        }
    } catch (error) {
        console.error('加载节假日数据失败:', error);
        showError('加载数据失败，请检查网络连接');
    } finally {
        hideLoading();
    }
}

/**
 * 渲染节假日表格
 */
function renderHolidayTable(holidays) {
    const tbody = document.getElementById('holidayTableBody');

    if (!holidays || holidays.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="empty-data">
                    <i class="fas fa-calendar-times"></i>
                    <div>暂无节假日数据</div>
                </td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = holidays.map((holiday, index) => `
        <tr>
            <td>${(currentPage - 1) * pageSize + index + 1}</td>
            <td>${formatDate(holiday.holidayDate)}</td>
            <td>${holiday.holidayName}</td>
            <td>${getHolidayTypeBadge(holiday.holidayType)}</td>
            <td>${getWorkdayBadge(holiday.isWorkday)}</td>
            <td>${holiday.yearNum}</td>
            <td>${holiday.remark || '-'}</td>
            <td>${formatDateTime(holiday.createdTime)}</td>
            <td>
                <div class="btn-group-sm">
                    <button type="button" class="btn btn-outline-primary btn-sm"
                            onclick="editHoliday(${holiday.id})" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm"
                            onclick="deleteHoliday(${holiday.id}, '${holiday.holidayName}')" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

/**
 * 搜索节假日
 */
function searchHolidays() {
    currentPage = 1;
    loadHolidays(currentPage);
}

/**
 * 新增节假日
 */
function addHoliday() {
    resetForm();
    document.getElementById('addHolidayModalLabel').innerHTML = '<i class="fas fa-plus me-2"></i>新增节假日';
    const modal = new bootstrap.Modal(document.getElementById('addHolidayModal'));
    modal.show();
}

/**
 * 编辑节假日
 */
async function editHoliday(id) {
    try {
        const response = await axios.get(`${API_BASE_URL}/${id}`);
        const data = response.data;

        if (data.success) {
            const holiday = data.data;
            fillForm(holiday);
            document.getElementById('addHolidayModalLabel').innerHTML = '<i class="fas fa-edit me-2"></i>编辑节假日';
            const modal = new bootstrap.Modal(document.getElementById('addHolidayModal'));
            modal.show();
        } else {
            showError('获取节假日信息失败：' + data.message);
        }
    } catch (error) {
        console.error('获取节假日信息失败:', error);
        showError('获取节假日信息失败');
    }
}

/**
 * 保存节假日
 */
async function saveHoliday() {
    try {
        const formData = getFormData();

        // 表单验证
        if (!validateForm(formData)) {
            return;
        }

        const isEdit = !!formData.id;
        const url = isEdit ? `${API_BASE_URL}/${formData.id}` : API_BASE_URL;
        const method = isEdit ? 'put' : 'post';

        const response = await axios[method](url, formData);
        const data = response.data;

        if (data.success) {
            showSuccess(isEdit ? '更新成功' : '新增成功');
            const modal = bootstrap.Modal.getInstance(document.getElementById('addHolidayModal'));
            modal.hide();
            loadHolidays(currentPage);
        } else {
            showError('保存失败：' + data.message);
        }
    } catch (error) {
        console.error('保存节假日失败:', error);
        showError('保存失败，请检查输入信息');
    }
}

/**
 * 删除节假日
 */
async function deleteHoliday(id, name) {
    if (!confirm(`确定要删除节假日"${name}"吗？`)) {
        return;
    }

    try {
        const response = await axios.delete(`${API_BASE_URL}/${id}`);
        const data = response.data;

        if (data.success) {
            showSuccess('删除成功');
            loadHolidays(currentPage);
        } else {
            showError('删除失败：' + data.message);
        }
    } catch (error) {
        console.error('删除节假日失败:', error);
        showError('删除失败');
    }
}

/**
 * 批量导入节假日
 */
function importHolidays() {
    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();
}

/**
 * 上传文件
 */
async function uploadFile() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];

    if (!file) {
        showError('请选择要导入的文件');
        return;
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
        const response = await axios.post(`${API_BASE_URL}/import`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });

        const data = response.data;
        if (data.success) {
            showSuccess(`导入成功，共导入${data.data}条记录`);
            const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
            modal.hide();
            loadHolidays(currentPage);
        } else {
            showError('导入失败：' + data.message);
        }
    } catch (error) {
        console.error('导入失败:', error);
        showError('导入失败，请检查文件格式');
    }
}

/**
 * 导出节假日数据
 */
async function exportHolidays() {
    try {
        const params = new URLSearchParams({
            year: document.getElementById('yearSelect').value || '',
            holidayType: document.getElementById('holidayTypeSelect').value || '',
            holidayName: document.getElementById('holidayName').value || ''
        });

        const response = await axios.get(`${API_BASE_URL}/export?${params}`, {
            responseType: 'blob'
        });

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `节假日数据_${new Date().toISOString().slice(0, 10)}.xlsx`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);

        showSuccess('导出成功');
    } catch (error) {
        console.error('导出失败:', error);
        showError('导出失败');
    }
}

/**
 * 下载导入模板
 */
function downloadTemplate() {
    const link = document.createElement('a');
    link.href = '/grain-risk/template/holiday_import_template.xlsx';
    link.setAttribute('download', '节假日导入模板.xlsx');
    document.body.appendChild(link);
    link.click();
    link.remove();
}

/**
 * 渲染分页
 */
function renderPagination(current, pages, total) {
    const pagination = document.getElementById('pagination');

    if (pages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // 上一页
    paginationHtml += `
        <li class="page-item ${current <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadHolidays(${current - 1})" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;

    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(pages, current + 2);

    if (startPage > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadHolidays(1)">1</a></li>`;
        if (startPage > 2) {
            paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === current ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadHolidays(${i})">${i}</a>
            </li>
        `;
    }

    if (endPage < pages) {
        if (endPage < pages - 1) {
            paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadHolidays(${pages})">${pages}</a></li>`;
    }

    // 下一页
    paginationHtml += `
        <li class="page-item ${current >= pages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadHolidays(${current + 1})" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;

    pagination.innerHTML = paginationHtml;
}

/**
 * 获取表单数据
 */
function getFormData() {
    return {
        id: document.getElementById('holidayId').value || null,
        holidayDate: document.getElementById('modalHolidayDate').value,
        holidayName: document.getElementById('modalHolidayName').value,
        holidayType: parseInt(document.getElementById('modalHolidayType').value),
        isWorkday: parseInt(document.getElementById('modalIsWorkday').value),
        yearNum: parseInt(document.getElementById('modalYearNum').value),
        remark: document.getElementById('modalRemark').value
    };
}

/**
 * 填充表单数据
 */
function fillForm(holiday) {
    document.getElementById('holidayId').value = holiday.id || '';
    document.getElementById('modalHolidayDate').value = holiday.holidayDate || '';
    document.getElementById('modalHolidayName').value = holiday.holidayName || '';
    document.getElementById('modalHolidayType').value = holiday.holidayType || '';
    document.getElementById('modalIsWorkday').value = holiday.isWorkday || 0;
    document.getElementById('modalYearNum').value = holiday.yearNum || '';
    document.getElementById('modalRemark').value = holiday.remark || '';

    // 更新日期选择器
    if (datePicker && holiday.holidayDate) {
        datePicker.setDate(holiday.holidayDate);
    }
}

/**
 * 重置表单
 */
function resetForm() {
    document.getElementById('holidayForm').reset();
    document.getElementById('holidayId').value = '';
    if (datePicker) {
        datePicker.clear();
    }
}

/**
 * 表单验证
 */
function validateForm(formData) {
    if (!formData.holidayDate) {
        showError('请选择节假日日期');
        return false;
    }

    if (!formData.holidayName.trim()) {
        showError('请输入节假日名称');
        return false;
    }

    if (!formData.holidayType) {
        showError('请选择节假日类型');
        return false;
    }

    if (!formData.yearNum) {
        showError('请选择年份');
        return false;
    }

    return true;
}

/**
 * 获取节假日类型标签
 */
function getHolidayTypeBadge(type) {
    const typeMap = {
        1: { text: '法定节假日', class: 'badge-legal' },
        2: { text: '调休', class: 'badge-adjust' },
        3: { text: '自定义', class: 'badge-custom' }
    };

    const typeInfo = typeMap[type] || { text: '未知', class: 'badge-secondary' };
    return `<span class="badge ${typeInfo.class} badge-holiday-type">${typeInfo.text}</span>`;
}

/**
 * 获取工作日标签
 */
function getWorkdayBadge(isWorkday) {
    if (isWorkday === 1) {
        return '<span class="badge badge-workday badge-holiday-type">工作日</span>';
    } else {
        return '<span class="badge badge-holiday badge-holiday-type">休息日</span>';
    }
}

/**
 * 格式化日期
 */
function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
    showMessage(message, 'success');
}

/**
 * 显示错误消息
 */
function showError(message) {
    showMessage(message, 'danger');
}

/**
 * 显示消息
 */
function showMessage(message, type) {
    // 移除现有的消息
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // 创建新的消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';

    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv && alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

/**
 * 显示加载状态
 */
function showLoading() {
    const tbody = document.getElementById('holidayTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="9" class="text-center py-4">
                <div class="loading me-2"></div>
                正在加载数据...
            </td>
        </tr>
    `;
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    // 加载状态会在数据渲染时被替换，这里不需要特殊处理
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 为搜索添加防抖
const debouncedSearch = debounce(searchHolidays, 300);