// 高危作业标准化流程管理系统 - 公共JavaScript库

// 全局配置
const CONFIG = {
    APP_NAME: '高危作业标准化流程管理系统',
    VERSION: '1.0.0',
    API_BASE_URL: '/api/v1',
    STORAGE_PREFIX: 'fumigation_system_',
    REFRESH_INTERVAL: 5000, // 数据刷新间隔(毫秒)
    NOTIFICATION_TIMEOUT: 3000 // 通知显示时间(毫秒)
};

// 工作流程步骤定义
const WORKFLOW_STEPS = [
    { id: 1, name: '熏蒸方案制定', key: 'plan', platform: 'web', status: 'completed' },
    { id: 2, name: '作业前准备', key: 'prepare', platform: 'app', status: 'completed' },
    { id: 3, name: '施药操作', key: 'apply', platform: 'app', status: 'active' },
    { id: 4, name: '补药操作', key: 'supplement', platform: 'app', status: 'pending' },
    { id: 5, name: '散气操作', key: 'ventilate', platform: 'app', status: 'pending' },
    { id: 6, name: '效果检查', key: 'check', platform: 'both', status: 'pending' },
    { id: 7, name: '作业总结', key: 'summary', platform: 'web', status: 'pending' }
];

// 用户角色定义
const USER_ROLES = {
    admin: { name: '系统管理员', level: 9 },
    director: { name: '粮库主任', level: 8 },
    safety_manager: { name: '安全主管', level: 7 },
    work_leader: { name: '作业负责人', level: 6 },
    safety_officer: { name: '安全员', level: 5 },
    warehouse_keeper: { name: '仓管员', level: 4 },
    operator: { name: '操作人员', level: 3 },
    inspector: { name: '检验员', level: 3 }
};

// 数据存储管理类
class StorageManager {
    static set(key, value) {
        try {
            localStorage.setItem(CONFIG.STORAGE_PREFIX + key, JSON.stringify(value));
            return true;
        } catch (error) {
            console.error('存储数据失败:', error);
            return false;
        }
    }

    static get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(CONFIG.STORAGE_PREFIX + key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取数据失败:', error);
            return defaultValue;
        }
    }

    static remove(key) {
        try {
            localStorage.removeItem(CONFIG.STORAGE_PREFIX + key);
            return true;
        } catch (error) {
            console.error('删除数据失败:', error);
            return false;
        }
    }

    static clear() {
        try {
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith(CONFIG.STORAGE_PREFIX)) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (error) {
            console.error('清空数据失败:', error);
            return false;
        }
    }
}

// 通知管理类
class NotificationManager {
    static show(message, type = 'info', duration = CONFIG.NOTIFICATION_TIMEOUT) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${this.getIcon(type)}</span>
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        // 添加样式
        if (!document.getElementById('notification-styles')) {
            const styles = document.createElement('style');
            styles.id = 'notification-styles';
            styles.textContent = `
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    min-width: 300px;
                    max-width: 500px;
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
                    z-index: 10000;
                    animation: slideInRight 0.3s ease-out;
                }
                .notification-content {
                    display: flex;
                    align-items: center;
                    padding: 16px;
                    gap: 12px;
                }
                .notification-icon {
                    font-size: 20px;
                    flex-shrink: 0;
                }
                .notification-message {
                    flex: 1;
                    font-size: 14px;
                    line-height: 1.4;
                }
                .notification-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    color: #999;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .notification-success { border-left: 4px solid #4CAF50; }
                .notification-warning { border-left: 4px solid #ff9800; }
                .notification-error { border-left: 4px solid #f44336; }
                .notification-info { border-left: 4px solid #2196F3; }
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(styles);
        }

        document.body.appendChild(notification);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        }

        return notification;
    }

    static getIcon(type) {
        const icons = {
            success: '✓',
            warning: '⚠',
            error: '✕',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    }

    static success(message, duration) {
        return this.show(message, 'success', duration);
    }

    static warning(message, duration) {
        return this.show(message, 'warning', duration);
    }

    static error(message, duration) {
        return this.show(message, 'error', duration);
    }

    static info(message, duration) {
        return this.show(message, 'info', duration);
    }
}

// 表单验证类
class FormValidator {
    constructor(form) {
        this.form = form;
        this.rules = {};
        this.errors = {};
    }

    addRule(fieldName, rule) {
        if (!this.rules[fieldName]) {
            this.rules[fieldName] = [];
        }
        this.rules[fieldName].push(rule);
        return this;
    }

    required(fieldName, message = '此字段为必填项') {
        return this.addRule(fieldName, {
            type: 'required',
            message,
            validate: (value) => value && value.toString().trim() !== ''
        });
    }

    minLength(fieldName, length, message) {
        return this.addRule(fieldName, {
            type: 'minLength',
            message: message || `最少需要${length}个字符`,
            validate: (value) => !value || value.toString().length >= length
        });
    }

    maxLength(fieldName, length, message) {
        return this.addRule(fieldName, {
            type: 'maxLength',
            message: message || `最多允许${length}个字符`,
            validate: (value) => !value || value.toString().length <= length
        });
    }

    pattern(fieldName, regex, message) {
        return this.addRule(fieldName, {
            type: 'pattern',
            message,
            validate: (value) => !value || regex.test(value.toString())
        });
    }

    custom(fieldName, validator, message) {
        return this.addRule(fieldName, {
            type: 'custom',
            message,
            validate: validator
        });
    }

    validate() {
        this.errors = {};
        const formData = new FormData(this.form);
        
        Object.keys(this.rules).forEach(fieldName => {
            const value = formData.get(fieldName);
            const fieldRules = this.rules[fieldName];
            
            for (let rule of fieldRules) {
                if (!rule.validate(value)) {
                    if (!this.errors[fieldName]) {
                        this.errors[fieldName] = [];
                    }
                    this.errors[fieldName].push(rule.message);
                    break; // 只显示第一个错误
                }
            }
        });

        this.displayErrors();
        return Object.keys(this.errors).length === 0;
    }

    displayErrors() {
        // 清除之前的错误显示
        this.form.querySelectorAll('.form-error').forEach(el => el.remove());
        this.form.querySelectorAll('.form-control.error').forEach(el => {
            el.classList.remove('error');
        });

        // 显示新的错误
        Object.keys(this.errors).forEach(fieldName => {
            const field = this.form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('error');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'form-error';
                errorDiv.textContent = this.errors[fieldName][0];
                field.parentElement.appendChild(errorDiv);
            }
        });
    }

    getErrors() {
        return this.errors;
    }
}

// 工作流程管理类
class WorkflowManager {
    constructor() {
        this.currentStep = StorageManager.get('current_step', 3);
        this.stepData = StorageManager.get('step_data', {});
        this.workflowData = StorageManager.get('workflow_data', this.getDefaultWorkflowData());
    }

    getDefaultWorkflowData() {
        return {
            applicationId: 'FUM-2024-001',
            warehouseId: '1号仓',
            grainType: '小麦',
            grainQuantity: 500,
            pestType: '玉米象',
            pestDensity: 15.2,
            fumigantType: '磷化铝',
            fumigantDosage: 15.5,
            fumigationPeriod: 168,
            temperatureRange: '20-25°C',
            humidityRange: '60-70%',
            applicant: '张三',
            applicantRole: 'work_leader',
            applicationTime: '2024-01-15 09:30:00',
            approvalStatus: 'approved',
            riskLevel: 'high',
            plannedStartTime: '2024-01-16 08:00:00',
            plannedEndTime: '2024-01-23 18:00:00'
        };
    }

    getCurrentStep() {
        return this.currentStep;
    }

    setCurrentStep(stepId) {
        this.currentStep = stepId;
        StorageManager.set('current_step', stepId);
        this.updateStepStatus(stepId, 'active');
        return this;
    }

    getStepData(stepId) {
        return this.stepData[stepId] || {};
    }

    setStepData(stepId, data) {
        this.stepData[stepId] = { ...this.stepData[stepId], ...data };
        StorageManager.set('step_data', this.stepData);
        return this;
    }

    completeStep(stepId, data = {}) {
        this.setStepData(stepId, { ...data, completedAt: new Date().toISOString() });
        this.updateStepStatus(stepId, 'completed');
        
        // 自动激活下一步
        const nextStep = stepId + 1;
        if (nextStep <= WORKFLOW_STEPS.length) {
            this.setCurrentStep(nextStep);
        }
        
        return this;
    }

    updateStepStatus(stepId, status) {
        const step = WORKFLOW_STEPS.find(s => s.id === stepId);
        if (step) {
            step.status = status;
        }
        return this;
    }

    getWorkflowData() {
        return this.workflowData;
    }

    updateWorkflowData(data) {
        this.workflowData = { ...this.workflowData, ...data };
        StorageManager.set('workflow_data', this.workflowData);
        return this;
    }

    getStepProgress() {
        const completedSteps = WORKFLOW_STEPS.filter(s => s.status === 'completed').length;
        return Math.round((completedSteps / WORKFLOW_STEPS.length) * 100);
    }

    canAccessStep(stepId, userRole) {
        const step = WORKFLOW_STEPS.find(s => s.id === stepId);
        if (!step) return false;

        // 检查步骤是否已解锁
        const prevStep = WORKFLOW_STEPS.find(s => s.id === stepId - 1);
        if (prevStep && prevStep.status !== 'completed' && stepId > 1) {
            return false;
        }

        // 检查用户权限
        const userLevel = USER_ROLES[userRole]?.level || 0;
        return userLevel >= 3; // 最低权限要求
    }
}

// 实时数据管理类
class RealTimeDataManager {
    constructor() {
        this.data = StorageManager.get('realtime_data', this.getDefaultData());
        this.updateInterval = null;
        this.callbacks = [];
    }

    getDefaultData() {
        return {
            temperature: 23.5,
            humidity: 65,
            ph3Concentration: 850,
            o2Concentration: 18.5,
            co2Concentration: 2.1,
            deviceStatus: 'normal',
            lastUpdate: new Date().toISOString()
        };
    }

    startAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }

        this.updateInterval = setInterval(() => {
            this.simulateDataUpdate();
        }, CONFIG.REFRESH_INTERVAL);

        return this;
    }

    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        return this;
    }

    simulateDataUpdate() {
        // 模拟真实的数据变化
        this.data.temperature += (Math.random() - 0.5) * 0.4;
        this.data.temperature = Math.max(20, Math.min(30, this.data.temperature));

        this.data.humidity += (Math.random() - 0.5) * 2;
        this.data.humidity = Math.max(50, Math.min(80, this.data.humidity));

        this.data.ph3Concentration += (Math.random() - 0.5) * 30;
        this.data.ph3Concentration = Math.max(800, Math.min(1000, this.data.ph3Concentration));

        this.data.o2Concentration += (Math.random() - 0.5) * 0.2;
        this.data.o2Concentration = Math.max(16, Math.min(21, this.data.o2Concentration));

        this.data.co2Concentration += (Math.random() - 0.5) * 0.1;
        this.data.co2Concentration = Math.max(1.5, Math.min(3, this.data.co2Concentration));

        this.data.lastUpdate = new Date().toISOString();

        // 检查异常情况
        if (this.data.ph3Concentration > 900) {
            this.data.deviceStatus = 'warning';
        } else if (this.data.ph3Concentration > 950) {
            this.data.deviceStatus = 'error';
        } else {
            this.data.deviceStatus = 'normal';
        }

        StorageManager.set('realtime_data', this.data);
        this.notifyCallbacks();
    }

    getData() {
        return { ...this.data };
    }

    subscribe(callback) {
        this.callbacks.push(callback);
        return this;
    }

    unsubscribe(callback) {
        const index = this.callbacks.indexOf(callback);
        if (index > -1) {
            this.callbacks.splice(index, 1);
        }
        return this;
    }

    notifyCallbacks() {
        this.callbacks.forEach(callback => {
            try {
                callback(this.getData());
            } catch (error) {
                console.error('回调函数执行失败:', error);
            }
        });
    }
}

// 工具函数
const Utils = {
    // 格式化日期时间
    formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    // 格式化数字
    formatNumber(num, decimals = 1) {
        if (typeof num !== 'number') return num;
        return num.toFixed(decimals);
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 生成UUID
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },

    // 深拷贝
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }
};

// 全局实例
window.workflowManager = new WorkflowManager();
window.realTimeDataManager = new RealTimeDataManager();
window.StorageManager = StorageManager;
window.NotificationManager = NotificationManager;
window.FormValidator = FormValidator;
window.Utils = Utils;

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 启动实时数据更新
    window.realTimeDataManager.startAutoUpdate();
    
    // 设置全局错误处理
    window.addEventListener('error', function(e) {
        console.error('全局错误:', e.error);
        NotificationManager.error('系统发生错误，请刷新页面重试');
    });
    
    // 设置页面卸载时的清理
    window.addEventListener('beforeunload', function() {
        window.realTimeDataManager.stopAutoUpdate();
    });
});

console.log('公共JavaScript库加载完成 - 版本:', CONFIG.VERSION);
