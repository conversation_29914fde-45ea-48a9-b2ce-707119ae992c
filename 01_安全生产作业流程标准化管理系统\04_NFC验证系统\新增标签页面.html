<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增NFC标签 - 中科抗菌管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }
        
        .breadcrumb a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            color: white;
        }
        
        .main-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .form-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }
        
        .form-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .form-header h1 {
            color: #2c3e50;
            font-size: 24px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border-left: 4px solid #007bff;
        }
        
        .form-section.product {
            border-left-color: #28a745;
        }
        
        .form-section.certificate {
            border-left-color: #ffc107;
        }
        
        .form-section.system {
            border-left-color: #dc3545;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .form-label.required::after {
            content: ' *';
            color: #dc3545;
        }
        
        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .form-textarea {
            height: 100px;
            resize: vertical;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-upload:hover {
            border-color: #007bff;
            background: #f8f9fa;
        }
        
        .file-upload.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .file-upload-icon {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .file-upload-text {
            color: #6c757d;
            font-size: 14px;
        }
        
        .file-list {
            margin-top: 15px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-icon {
            font-size: 20px;
            color: #007bff;
        }
        
        .file-name {
            font-size: 14px;
            color: #2c3e50;
        }
        
        .file-size {
            font-size: 12px;
            color: #6c757d;
        }
        
        .file-remove {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: 16px;
            height: 16px;
        }
        
        .checkbox-item label {
            font-size: 14px;
            color: #2c3e50;
            cursor: pointer;
        }
        
        .tag-input-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            min-height: 45px;
            cursor: text;
        }
        
        .tag-input-container:focus-within {
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .tag-item {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .tag-remove {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 14px;
            padding: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .tag-remove:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .tag-input {
            border: none;
            outline: none;
            flex: 1;
            min-width: 100px;
            padding: 4px;
            font-size: 14px;
        }
        
        .form-actions {
            grid-column: 1 / -1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #e9ecef;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 2px solid #6c757d;
        }
        
        .btn-outline:hover {
            background: #6c757d;
            color: white;
        }
        
        .help-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
            line-height: 1.4;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        
        .alert-warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        
        .preview-section {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .preview-section h3 {
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .preview-content {
            background: white;
            border-radius: 6px;
            padding: 15px;
            border: 1px solid #bbdefb;
        }
        
        .back-button {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <div class="breadcrumb">
                <a href="管理端主界面.html">🏠 首页</a>
                <span>></span>
                <a href="管理端主界面.html">NFC标签管理</a>
                <span>></span>
                <span>新增标签</span>
            </div>
            <div>
                <button class="btn btn-outline" onclick="window.close()">关闭</button>
            </div>
        </div>
    </div>
    
    <div class="main-container">
        <div class="back-button">
            <button class="btn btn-outline" onclick="window.history.back()">
                ← 返回列表
            </button>
        </div>
        
        <div class="form-container">
            <div class="form-header">
                <span style="font-size: 32px;">➕</span>
                <h1>新增NFC标签</h1>
            </div>
            
            <div class="alert alert-info">
                <strong>📋 填写说明</strong><br>
                请完整填写标签信息，带 * 号的为必填项。标签创建后将生成唯一ID，用于产品防伪验证。
            </div>
            
            <form id="tagForm" class="form-grid">
                <!-- 基本信息 -->
                <div class="form-section">
                    <h2 class="section-title">
                        🏷️ 基本信息
                    </h2>
                    
                    <div class="form-group">
                        <label class="form-label required">标签ID</label>
                        <input type="text" class="form-input" id="tagId" readonly 
                               value="NFC-ZK-20240325-001" 
                               style="background: #f8f9fa; color: #6c757d;">
                        <div class="help-text">系统自动生成，不可修改</div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label required">标签状态</label>
                        <select class="form-select" id="tagStatus" required>
                            <option value="">请选择状态</option>
                            <option value="active" selected>有效</option>
                            <option value="inactive">无效</option>
                            <option value="pending">待审核</option>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">创建日期</label>
                            <input type="date" class="form-input" id="createDate" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">有效期限</label>
                            <input type="date" class="form-input" id="expireDate" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">备注说明</label>
                        <textarea class="form-textarea" id="tagNotes" 
                                  placeholder="请输入标签备注信息..."></textarea>
                    </div>
                </div>
                
                <!-- 产品信息 -->
                <div class="form-section product">
                    <h2 class="section-title">
                        📦 产品信息
                    </h2>
                    
                    <div class="form-group">
                        <label class="form-label required">产品名称</label>
                        <input type="text" class="form-input" id="productName" required
                               placeholder="请输入产品名称">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">产品型号</label>
                            <input type="text" class="form-input" id="productModel" required
                                   placeholder="如：ZK-AB-2024">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">生产批次</label>
                            <input type="text" class="form-input" id="productBatch" required
                                   placeholder="如：20240315001">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">抗菌等级</label>
                            <select class="form-select" id="antibacterialLevel" required>
                                <option value="">请选择等级</option>
                                <option value="AAA">AAA级</option>
                                <option value="AA">AA级</option>
                                <option value="A">A级</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">生产日期</label>
                            <input type="date" class="form-input" id="productionDate" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">产品描述</label>
                        <textarea class="form-textarea" id="productDescription"
                                  placeholder="请输入产品详细描述..."></textarea>
                    </div>
                </div>

                <!-- 认证信息 -->
                <div class="form-section certificate">
                    <h2 class="section-title">
                        🏆 认证信息
                    </h2>

                    <div class="form-group">
                        <label class="form-label required">专利证书编号</label>
                        <input type="text" class="form-input" id="patentNumber" required
                               placeholder="如：ZL202410123456.7">
                    </div>

                    <div class="form-group">
                        <label class="form-label">获奖情况</label>
                        <div class="tag-input-container" onclick="focusTagInput()">
                            <div id="awardTags"></div>
                            <input type="text" class="tag-input" id="awardInput"
                                   placeholder="输入获奖名称后按回车添加"
                                   onkeypress="handleTagInput(event, 'award')">
                        </div>
                        <div class="help-text">按回车键添加获奖项目，可添加多个</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label required">检测报告编号</label>
                        <input type="text" class="form-input" id="testReportNumber" required
                               placeholder="如：ISO20743-2024-001">
                    </div>

                    <div class="form-group">
                        <label class="form-label">认证机构</label>
                        <input type="text" class="form-input" id="certificationOrg"
                               placeholder="如：中国纺织工业联合会">
                    </div>

                    <div class="form-group">
                        <label class="form-label">技术参数</label>
                        <div class="tag-input-container" onclick="focusTagInput()">
                            <div id="paramTags"></div>
                            <input type="text" class="tag-input" id="paramInput"
                                   placeholder="输入技术参数后按回车添加"
                                   onkeypress="handleTagInput(event, 'param')">
                        </div>
                        <div class="help-text">如：抗菌率>99%、材料成分等</div>
                    </div>
                </div>

                <!-- 系统设置 -->
                <div class="form-section system">
                    <h2 class="section-title">
                        ⚙️ 系统设置
                    </h2>

                    <div class="form-group">
                        <label class="form-label">验证权限</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="allowPublicVerify" checked>
                                <label for="allowPublicVerify">允许公开验证</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="allowOfflineVerify">
                                <label for="allowOfflineVerify">支持离线验证</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="requireLocation">
                                <label for="requireLocation">验证时记录位置</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="enableNotification" checked>
                                <label for="enableNotification">启用验证通知</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">验证限制</label>
                            <select class="form-select" id="verifyLimit">
                                <option value="unlimited">无限制</option>
                                <option value="daily">每日限制</option>
                                <option value="total">总次数限制</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">限制次数</label>
                            <input type="number" class="form-input" id="limitCount"
                                   placeholder="0表示无限制" min="0">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">关联视频URL</label>
                        <input type="url" class="form-input" id="videoUrl"
                               placeholder="https://example.com/video.mp4">
                        <div class="help-text">专利获奖展示视频链接</div>
                    </div>
                </div>

                <!-- 文件上传 -->
                <div class="form-section full-width">
                    <h2 class="section-title">
                        📎 相关文件
                    </h2>

                    <div class="form-group">
                        <label class="form-label">上传文件</label>
                        <div class="file-upload" id="fileUpload"
                             ondrop="handleFileDrop(event)"
                             ondragover="handleDragOver(event)"
                             ondragleave="handleDragLeave(event)"
                             onclick="document.getElementById('fileInput').click()">
                            <div class="file-upload-icon">📁</div>
                            <div class="file-upload-text">
                                点击选择文件或拖拽文件到此处<br>
                                <small>支持：PDF、JPG、PNG、DOC等格式，单个文件不超过10MB</small>
                            </div>
                        </div>
                        <input type="file" id="fileInput" multiple style="display: none;"
                               onchange="handleFileSelect(event)">
                        <div id="fileList" class="file-list"></div>
                    </div>
                </div>

                <!-- 预览区域 -->
                <div class="form-section full-width">
                    <div class="preview-section">
                        <h3>
                            👀 标签预览
                        </h3>
                        <div class="preview-content">
                            <div id="tagPreview">
                                <p style="color: #6c757d; text-align: center; padding: 20px;">
                                    填写表单信息后，这里将显示标签预览效果
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <div>
                        <button type="button" class="btn btn-outline" onclick="resetForm()">
                            🔄 重置表单
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="saveAsDraft()">
                            💾 保存草稿
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="previewTag()">
                            👀 预览效果
                        </button>
                        <button type="submit" class="btn btn-success">
                            ✅ 创建标签
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        window.addEventListener('load', function() {
            // 设置默认日期
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('createDate').value = today;

            // 设置默认过期日期（3年后）
            const expireDate = new Date();
            expireDate.setFullYear(expireDate.getFullYear() + 3);
            document.getElementById('expireDate').value = expireDate.toISOString().split('T')[0];

            // 生成新的标签ID
            generateNewTagId();

            // 绑定表单变化事件
            bindFormEvents();
        });

        // 生成新的标签ID
        function generateNewTagId() {
            const today = new Date();
            const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
            const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            const tagId = `NFC-ZK-${dateStr}-${randomNum}`;
            document.getElementById('tagId').value = tagId;
        }

        // 绑定表单事件
        function bindFormEvents() {
            const form = document.getElementById('tagForm');
            const inputs = form.querySelectorAll('input, select, textarea');

            inputs.forEach(input => {
                input.addEventListener('input', updatePreview);
                input.addEventListener('change', updatePreview);
            });
        }

        // 处理标签输入
        function handleTagInput(event, type) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const input = event.target;
                const value = input.value.trim();

                if (value) {
                    addTag(type, value);
                    input.value = '';
                }
            }
        }

        // 添加标签
        function addTag(type, value) {
            const container = document.getElementById(type + 'Tags');
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `
                <span>${value}</span>
                <button type="button" class="tag-remove" onclick="removeTag(this)">×</button>
            `;
            container.appendChild(tagElement);
            updatePreview();
        }

        // 移除标签
        function removeTag(button) {
            button.parentElement.remove();
            updatePreview();
        }

        // 聚焦标签输入
        function focusTagInput() {
            const activeInput = document.querySelector('.tag-input:focus');
            if (!activeInput) {
                document.querySelector('.tag-input').focus();
            }
        }

        // 文件拖拽处理
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = event.dataTransfer.files;
            handleFiles(files);
        }

        function handleFileSelect(event) {
            const files = event.target.files;
            handleFiles(files);
        }

        // 处理文件
        function handleFiles(files) {
            const fileList = document.getElementById('fileList');

            Array.from(files).forEach(file => {
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过10MB限制`);
                    return;
                }

                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <span class="file-icon">📄</span>
                        <div>
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <button type="button" class="file-remove" onclick="removeFile(this)">删除</button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 移除文件
        function removeFile(button) {
            button.parentElement.remove();
        }

        // 更新预览
        function updatePreview() {
            const preview = document.getElementById('tagPreview');
            const formData = getFormData();

            if (formData.productName) {
                preview.innerHTML = `
                    <div style="border: 2px solid #28a745; border-radius: 8px; padding: 15px;">
                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                            <span style="font-size: 24px;">✅</span>
                            <div>
                                <div style="font-weight: bold; color: #28a745;">正版产品</div>
                                <div style="font-size: 12px; color: #6c757d;">${formData.tagId}</div>
                            </div>
                        </div>
                        <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px;">${formData.productName}</div>
                        <div style="font-size: 14px; color: #6c757d;">
                            型号：${formData.productModel || '未填写'} |
                            批次：${formData.productBatch || '未填写'} |
                            等级：${formData.antibacterialLevel || '未填写'}
                        </div>
                        ${formData.patentNumber ? `<div style="font-size: 12px; color: #007bff; margin-top: 5px;">专利：${formData.patentNumber}</div>` : ''}
                    </div>
                `;
            } else {
                preview.innerHTML = `
                    <p style="color: #6c757d; text-align: center; padding: 20px;">
                        填写表单信息后，这里将显示标签预览效果
                    </p>
                `;
            }
        }

        // 获取表单数据
        function getFormData() {
            return {
                tagId: document.getElementById('tagId').value,
                productName: document.getElementById('productName').value,
                productModel: document.getElementById('productModel').value,
                productBatch: document.getElementById('productBatch').value,
                antibacterialLevel: document.getElementById('antibacterialLevel').value,
                patentNumber: document.getElementById('patentNumber').value
            };
        }

        // 预览标签
        function previewTag() {
            const formData = getFormData();
            if (!formData.productName) {
                alert('请先填写产品名称');
                return;
            }

            // 打开预览窗口
            const previewUrl = `APP端验证页面.html?preview=true&data=${encodeURIComponent(JSON.stringify(formData))}`;
            window.open(previewUrl, '_blank');
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置表单吗？所有填写的内容将丢失。')) {
                document.getElementById('tagForm').reset();
                document.getElementById('fileList').innerHTML = '';
                document.getElementById('awardTags').innerHTML = '';
                document.getElementById('paramTags').innerHTML = '';
                generateNewTagId();
                updatePreview();
            }
        }

        // 保存草稿
        function saveAsDraft() {
            const formData = getFormData();
            localStorage.setItem('nfc_tag_draft', JSON.stringify(formData));
            alert('草稿已保存！');
        }

        // 表单提交
        document.getElementById('tagForm').addEventListener('submit', function(event) {
            event.preventDefault();

            if (!validateForm()) {
                return;
            }

            const formData = getFormData();

            // 模拟提交
            if (confirm('确定要创建此NFC标签吗？')) {
                // 这里应该调用API提交数据
                alert('NFC标签创建成功！\n\n标签ID：' + formData.tagId + '\n\n页面将跳转到标签详情页面。');

                // 跳转到详情页面
                window.location.href = `标签详情页面.html?id=${formData.tagId}`;
            }
        });

        // 表单验证
        function validateForm() {
            const requiredFields = [
                'productName', 'productModel', 'productBatch',
                'antibacterialLevel', 'productionDate', 'patentNumber', 'testReportNumber'
            ];

            for (let field of requiredFields) {
                const element = document.getElementById(field);
                if (!element.value.trim()) {
                    alert(`请填写${element.previousElementSibling.textContent.replace(' *', '')}`);
                    element.focus();
                    return false;
                }
            }

            return true;
        }
    </script>
</body>
</html>
