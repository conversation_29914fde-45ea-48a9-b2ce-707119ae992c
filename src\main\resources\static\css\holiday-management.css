/* 节假日管理系统自定义样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.2rem;
}

/* 侧边栏样式 */
.list-group-item {
    border: none;
    border-radius: 0;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #e9ecef;
    transform: translateX(5px);
}

.list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

/* 状态标签样式 */
.badge-holiday-type {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.badge-legal {
    background-color: #dc3545;
}

.badge-adjust {
    background-color: #fd7e14;
}

.badge-custom {
    background-color: #6f42c1;
}

.badge-workday {
    background-color: #198754;
}

.badge-holiday {
    background-color: #6c757d;
}

/* 按钮样式 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 操作按钮组 */
.btn-group-sm .btn {
    margin-right: 0.25rem;
}

.btn-group-sm .btn:last-child {
    margin-right: 0;
}

/* 搜索表单样式 */
.form-label {
    font-weight: 500;
    color: #495057;
}

.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: #0d6efd;
    border-color: #dee2e6;
}

.page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 模态框样式 */
.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* 日期选择器样式 */
.flatpickr-input {
    background-color: #fff;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .col-md-2 {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
        font-size: 0.75rem;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 空数据提示 */
.empty-data {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 成功/错误消息样式 */
.alert {
    border: none;
    border-radius: 0.5rem;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}