<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流程节点配置 - 可视化编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            min-height: 600px;
        }

        .sidebar {
            background: #f8f9fa;
            padding: 20px;
            border-right: 1px solid #e9ecef;
        }

        .sidebar h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .node-library {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .node-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            cursor: grab;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .node-item:hover {
            border-color: #007bff;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .node-item:active {
            cursor: grabbing;
        }

        .node-icon {
            font-size: 20px;
            width: 24px;
            text-align: center;
        }

        .node-info {
            flex: 1;
        }

        .node-title {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 2px;
        }

        .node-desc {
            font-size: 12px;
            color: #6c757d;
        }

        .flow-editor {
            padding: 20px;
            background: #fafafa;
            position: relative;
        }

        .editor-toolbar {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .toolbar-left {
            display: flex;
            gap: 10px;
        }

        .toolbar-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: #f8f9fa;
            border-color: #007bff;
        }

        .toolbar-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .flow-canvas {
            background: white;
            border-radius: 12px;
            padding: 30px;
            min-height: 500px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            position: relative;
        }

        .flow-path {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        .flow-node {
            background: #007bff;
            color: white;
            padding: 15px 20px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 200px;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
        }

        .flow-node:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,123,255,0.4);
        }

        .flow-node.required {
            background: #28a745;
            box-shadow: 0 4px 15px rgba(40,167,69,0.3);
        }

        .flow-node.optional {
            background: #6c757d;
            box-shadow: 0 4px 15px rgba(108,117,125,0.3);
        }

        .flow-node.new {
            background: #ffc107;
            color: #212529;
            box-shadow: 0 4px 15px rgba(255,193,7,0.3);
        }

        .flow-node.selected {
            outline: 3px solid #ff6b6b;
            outline-offset: 3px;
        }

        .node-controls {
            position: absolute;
            top: -10px;
            right: -10px;
            display: none;
            gap: 5px;
        }

        .flow-node:hover .node-controls {
            display: flex;
        }

        .control-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .control-btn.move-up {
            background: #17a2b8;
            color: white;
        }

        .control-btn.move-down {
            background: #17a2b8;
            color: white;
        }

        .control-btn.delete {
            background: #dc3545;
            color: white;
        }

        .control-btn.edit {
            background: #ffc107;
            color: #212529;
        }

        .flow-arrow {
            color: #6c757d;
            font-size: 24px;
            margin: 5px 0;
        }

        .drop-zone {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            margin: 10px 0;
            transition: all 0.3s ease;
        }

        .drop-zone.active {
            border-color: #007bff;
            background: #f8f9fa;
            color: #007bff;
        }

        .node-badge {
            position: absolute;
            top: -8px;
            left: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .node-badge.required {
            background: #28a745;
        }

        .node-badge.new {
            background: #ffc107;
            color: #212529;
        }

        .properties-panel {
            position: fixed;
            right: -350px;
            top: 0;
            width: 350px;
            height: 100vh;
            background: white;
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .properties-panel.active {
            right: 0;
        }

        .properties-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .properties-content {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .action-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔀 流程节点配置</h1>
            <p>拖拽节点构建流程，调整顺序，自定义您的作业流程</p>
        </div>

        <div class="main-content">
            <!-- 左侧节点库 -->
            <div class="sidebar">
                <h3>📦 节点库</h3>
                <div class="node-library">
                    <div class="node-item" draggable="true" data-type="start">
                        <span class="node-icon">🟢</span>
                        <div class="node-info">
                            <div class="node-title">开始节点</div>
                            <div class="node-desc">流程起始点</div>
                        </div>
                    </div>

                    <div class="node-item" draggable="true" data-type="approval">
                        <span class="node-icon">✅</span>
                        <div class="node-info">
                            <div class="node-title">审批节点</div>
                            <div class="node-desc">需要人工审批</div>
                        </div>
                    </div>

                    <div class="node-item" draggable="true" data-type="check">
                        <span class="node-icon">🔍</span>
                        <div class="node-info">
                            <div class="node-title">检查节点</div>
                            <div class="node-desc">安全检查确认</div>
                        </div>
                    </div>

                    <div class="node-item" draggable="true" data-type="execute">
                        <span class="node-icon">⚙️</span>
                        <div class="node-info">
                            <div class="node-title">执行节点</div>
                            <div class="node-desc">作业执行</div>
                        </div>
                    </div>

                    <div class="node-item" draggable="true" data-type="monitor">
                        <span class="node-icon">👁️</span>
                        <div class="node-info">
                            <div class="node-title">监控节点</div>
                            <div class="node-desc">实时监控</div>
                        </div>
                    </div>

                    <div class="node-item" draggable="true" data-type="photo">
                        <span class="node-icon">📷</span>
                        <div class="node-info">
                            <div class="node-title">拍照节点</div>
                            <div class="node-desc">现场拍照记录</div>
                        </div>
                    </div>

                    <div class="node-item" draggable="true" data-type="notify">
                        <span class="node-icon">📢</span>
                        <div class="node-info">
                            <div class="node-title">通知节点</div>
                            <div class="node-desc">消息通知</div>
                        </div>
                    </div>

                    <div class="node-item" draggable="true" data-type="test">
                        <span class="node-icon">🧪</span>
                        <div class="node-info">
                            <div class="node-title">检测节点</div>
                            <div class="node-desc">气体检测</div>
                        </div>
                    </div>

                    <div class="node-item" draggable="true" data-type="end">
                        <span class="node-icon">🔴</span>
                        <div class="node-info">
                            <div class="node-title">结束节点</div>
                            <div class="node-desc">流程结束</div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 30px;">
                    <h3>💡 操作提示</h3>
                    <div style="font-size: 12px; color: #6c757d; line-height: 1.4;">
                        • 拖拽节点到右侧画布<br>
                        • 点击节点可编辑属性<br>
                        • 使用箭头调整顺序<br>
                        • 红色X删除节点<br>
                        • 绿色节点为必需节点
                    </div>
                </div>
            </div>

            <!-- 右侧流程编辑器 -->
            <div class="flow-editor">
                <div class="editor-toolbar">
                    <div class="toolbar-left">
                        <button class="toolbar-btn active">🔥 动火作业</button>
                        <button class="toolbar-btn">🏠 有限空间</button>
                        <button class="toolbar-btn">⬆️ 高处作业</button>
                        <button class="toolbar-btn">🔧 设备检修</button>
                    </div>
                    <div>
                        <button class="toolbar-btn" onclick="resetFlow()">🔄 重置</button>
                        <button class="toolbar-btn" onclick="previewFlow()">👀 预览</button>
                        <button class="toolbar-btn" onclick="saveFlow()">💾 保存</button>
                    </div>
                </div>

                <div class="flow-canvas" id="flowCanvas">
                    <div class="flow-path" id="flowPath">
                        <!-- 开始节点（必需，不可删除） -->
                        <div class="flow-node required" data-id="1" data-type="start" onclick="editNode(this)">
                            <span class="node-badge required">必</span>
                            <span class="node-icon">🟢</span>
                            <span>申请提交</span>
                        </div>

                        <div class="flow-arrow">↓</div>

                        <!-- 可拖拽的投放区域 -->
                        <div class="drop-zone" ondrop="dropNode(event)" ondragover="allowDrop(event)">
                            拖拽节点到这里添加新步骤
                        </div>

                        <!-- 部门审批节点 -->
                        <div class="flow-node" data-id="2" data-type="approval" onclick="editNode(this)">
                            <div class="node-controls">
                                <button class="control-btn move-up" onclick="moveNode(this, 'up')" title="上移">↑</button>
                                <button class="control-btn move-down" onclick="moveNode(this, 'down')" title="下移">↓</button>
                                <button class="control-btn edit" onclick="editNode(this.parentNode.parentNode)" title="编辑">✏️</button>
                                <button class="control-btn delete" onclick="deleteNode(this)" title="删除">×</button>
                            </div>
                            <span class="node-icon">✅</span>
                            <span>部门审批</span>
                        </div>

                        <div class="flow-arrow">↓</div>

                        <div class="drop-zone" ondrop="dropNode(event)" ondragover="allowDrop(event)">
                            拖拽节点到这里添加新步骤
                        </div>

                        <!-- 安全检查节点 -->
                        <div class="flow-node" data-id="3" data-type="check" onclick="editNode(this)">
                            <div class="node-controls">
                                <button class="control-btn move-up" onclick="moveNode(this, 'up')" title="上移">↑</button>
                                <button class="control-btn move-down" onclick="moveNode(this, 'down')" title="下移">↓</button>
                                <button class="control-btn edit" onclick="editNode(this.parentNode.parentNode)" title="编辑">✏️</button>
                                <button class="control-btn delete" onclick="deleteNode(this)" title="删除">×</button>
                            </div>
                            <span class="node-icon">🔍</span>
                            <span>安全检查</span>
                        </div>

                        <div class="flow-arrow">↓</div>

                        <div class="drop-zone" ondrop="dropNode(event)" ondragover="allowDrop(event)">
                            拖拽节点到这里添加新步骤
                        </div>

                        <!-- 开始作业节点 -->
                        <div class="flow-node" data-id="4" data-type="execute" onclick="editNode(this)">
                            <div class="node-controls">
                                <button class="control-btn move-up" onclick="moveNode(this, 'up')" title="上移">↑</button>
                                <button class="control-btn move-down" onclick="moveNode(this, 'down')" title="下移">↓</button>
                                <button class="control-btn edit" onclick="editNode(this.parentNode.parentNode)" title="编辑">✏️</button>
                                <button class="control-btn delete" onclick="deleteNode(this)" title="删除">×</button>
                            </div>
                            <span class="node-icon">⚙️</span>
                            <span>开始作业</span>
                        </div>

                        <div class="flow-arrow">↓</div>

                        <div class="drop-zone" ondrop="dropNode(event)" ondragover="allowDrop(event)">
                            拖拽节点到这里添加新步骤
                        </div>

                        <!-- 结束节点（必需，不可删除） -->
                        <div class="flow-node required" data-id="5" data-type="end" onclick="editNode(this)">
                            <span class="node-badge required">必</span>
                            <span class="node-icon">🔴</span>
                            <span>作业验收</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 节点属性编辑面板 -->
    <div class="properties-panel" id="propertiesPanel">
        <div class="properties-header">
            <h3>节点属性</h3>
            <button onclick="closeProperties()" style="background: none; border: none; color: white; font-size: 20px; cursor: pointer;">×</button>
        </div>
        <div class="properties-content">
            <div class="form-group">
                <label>节点名称</label>
                <input type="text" id="nodeName" placeholder="请输入节点名称">
            </div>

            <div class="form-group">
                <label>节点类型</label>
                <select id="nodeType">
                    <option value="approval">审批节点</option>
                    <option value="check">检查节点</option>
                    <option value="execute">执行节点</option>
                    <option value="monitor">监控节点</option>
                    <option value="photo">拍照节点</option>
                    <option value="notify">通知节点</option>
                    <option value="test">检测节点</option>
                </select>
            </div>

            <div class="form-group">
                <label>处理人</label>
                <select id="nodeAssignee">
                    <option value="applicant">申请人</option>
                    <option value="dept_manager">部门主管</option>
                    <option value="safety_manager">安全主管</option>
                    <option value="general_manager">总经理</option>
                    <option value="safety_officer">安全员</option>
                    <option value="inspector">检查员</option>
                    <option value="operator">作业人员</option>
                    <option value="monitor">监护人</option>
                </select>
            </div>

            <div class="form-group">
                <label>处理时限（小时）</label>
                <input type="number" id="nodeTimeout" placeholder="请输入时限" min="0.5" max="72" step="0.5">
            </div>

            <div class="form-group">
                <label>节点描述</label>
                <textarea id="nodeDescription" placeholder="请输入节点描述"></textarea>
            </div>

            <div class="form-group">
                <label>是否必需</label>
                <select id="nodeRequired">
                    <option value="false">可选节点</option>
                    <option value="true">必需节点</option>
                </select>
            </div>

            <div style="display: flex; gap: 10px; margin-top: 20px;">
                <button class="btn btn-primary" onclick="saveNodeProperties()">保存</button>
                <button class="btn btn-secondary" onclick="closeProperties()">取消</button>
            </div>
        </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="action-buttons">
        <button class="btn btn-secondary" onclick="window.history.back()">返回</button>
        <button class="btn btn-success" onclick="saveFlow()">保存流程</button>
    </div>

    <script>
        let currentEditingNode = null;
        let nodeIdCounter = 6;

        // 节点类型配置
        const nodeConfig = {
            start: { icon: '🟢', name: '开始节点', color: '#28a745' },
            approval: { icon: '✅', name: '审批节点', color: '#007bff' },
            check: { icon: '🔍', name: '检查节点', color: '#17a2b8' },
            execute: { icon: '⚙️', name: '执行节点', color: '#6f42c1' },
            monitor: { icon: '👁️', name: '监控节点', color: '#fd7e14' },
            photo: { icon: '📷', name: '拍照节点', color: '#20c997' },
            notify: { icon: '📢', name: '通知节点', color: '#ffc107' },
            test: { icon: '🧪', name: '检测节点', color: '#e83e8c' },
            end: { icon: '🔴', name: '结束节点', color: '#dc3545' }
        };

        // 拖拽功能
        function allowDrop(ev) {
            ev.preventDefault();
            ev.target.classList.add('active');
        }

        function dropNode(ev) {
            ev.preventDefault();
            ev.target.classList.remove('active');

            const nodeType = ev.dataTransfer.getData("text");
            if (nodeType) {
                addNode(ev.target, nodeType);
            }
        }

        // 添加拖拽事件监听
        document.querySelectorAll('.node-item').forEach(item => {
            item.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData("text", this.dataset.type);
            });
        });

        // 添加节点
        function addNode(dropZone, nodeType) {
            const config = nodeConfig[nodeType];
            if (!config) return;

            const nodeId = nodeIdCounter++;
            const nodeElement = document.createElement('div');
            nodeElement.className = 'flow-node new';
            nodeElement.dataset.id = nodeId;
            nodeElement.dataset.type = nodeType;
            nodeElement.onclick = () => editNode(nodeElement);

            nodeElement.innerHTML = `
                <div class="node-controls">
                    <button class="control-btn move-up" onclick="moveNode(this, 'up')" title="上移">↑</button>
                    <button class="control-btn move-down" onclick="moveNode(this, 'down')" title="下移">↓</button>
                    <button class="control-btn edit" onclick="editNode(this.parentNode.parentNode)" title="编辑">✏️</button>
                    <button class="control-btn delete" onclick="deleteNode(this)" title="删除">×</button>
                </div>
                <span class="node-badge new">新</span>
                <span class="node-icon">${config.icon}</span>
                <span>${config.name}</span>
            `;

            // 在投放区域前插入新节点
            const flowPath = document.getElementById('flowPath');
            const arrow = document.createElement('div');
            arrow.className = 'flow-arrow';
            arrow.textContent = '↓';

            flowPath.insertBefore(arrow, dropZone);
            flowPath.insertBefore(nodeElement, dropZone);

            // 添加新的投放区域
            const newDropZone = document.createElement('div');
            newDropZone.className = 'drop-zone';
            newDropZone.textContent = '拖拽节点到这里添加新步骤';
            newDropZone.ondrop = dropNode;
            newDropZone.ondragover = allowDrop;

            flowPath.insertBefore(newDropZone, dropZone);
            flowPath.insertBefore(document.createElement('div'), dropZone).className = 'flow-arrow';
            flowPath.insertBefore(document.createElement('div'), dropZone).textContent = '↓';
        }

        // 编辑节点
        function editNode(node) {
            currentEditingNode = node;

            // 移除其他节点的选中状态
            document.querySelectorAll('.flow-node').forEach(n => n.classList.remove('selected'));

            // 添加当前节点的选中状态
            node.classList.add('selected');

            // 填充属性面板
            const nodeName = node.querySelector('span:last-child').textContent;
            const nodeType = node.dataset.type;

            document.getElementById('nodeName').value = nodeName;
            document.getElementById('nodeType').value = nodeType;

            // 显示属性面板
            document.getElementById('propertiesPanel').classList.add('active');
        }

        // 关闭属性面板
        function closeProperties() {
            document.getElementById('propertiesPanel').classList.remove('active');
            document.querySelectorAll('.flow-node').forEach(n => n.classList.remove('selected'));
            currentEditingNode = null;
        }

        // 保存节点属性
        function saveNodeProperties() {
            if (!currentEditingNode) return;

            const nodeName = document.getElementById('nodeName').value;
            const nodeType = document.getElementById('nodeType').value;
            const nodeRequired = document.getElementById('nodeRequired').value === 'true';

            // 更新节点显示
            const nameSpan = currentEditingNode.querySelector('span:last-child');
            nameSpan.textContent = nodeName;

            // 更新节点类型
            currentEditingNode.dataset.type = nodeType;
            const iconSpan = currentEditingNode.querySelector('.node-icon');
            iconSpan.textContent = nodeConfig[nodeType].icon;

            // 更新必需状态
            const badge = currentEditingNode.querySelector('.node-badge');
            if (nodeRequired) {
                badge.textContent = '必';
                badge.className = 'node-badge required';
                currentEditingNode.classList.add('required');
            } else {
                badge.textContent = '选';
                badge.className = 'node-badge';
                currentEditingNode.classList.remove('required');
            }

            closeProperties();
        }

        // 移动节点
        function moveNode(button, direction) {
            const node = button.closest('.flow-node');
            const flowPath = document.getElementById('flowPath');
            const children = Array.from(flowPath.children);
            const nodeIndex = children.indexOf(node);

            if (direction === 'up' && nodeIndex > 2) {
                // 向上移动（跳过箭头和投放区域）
                const targetIndex = nodeIndex - 3;
                flowPath.insertBefore(node, children[targetIndex]);
            } else if (direction === 'down' && nodeIndex < children.length - 3) {
                // 向下移动
                const targetIndex = nodeIndex + 3;
                if (targetIndex < children.length) {
                    flowPath.insertBefore(node, children[targetIndex + 1]);
                }
            }
        }

        // 删除节点
        function deleteNode(button) {
            const node = button.closest('.flow-node');

            // 不能删除必需节点
            if (node.classList.contains('required')) {
                alert('必需节点不能删除！');
                return;
            }

            if (confirm('确定要删除这个节点吗？')) {
                const flowPath = document.getElementById('flowPath');
                const children = Array.from(flowPath.children);
                const nodeIndex = children.indexOf(node);

                // 删除节点及其相关的箭头和投放区域
                if (nodeIndex > 0) {
                    flowPath.removeChild(children[nodeIndex - 1]); // 删除前面的箭头
                }
                if (nodeIndex < children.length - 1) {
                    flowPath.removeChild(children[nodeIndex + 1]); // 删除后面的投放区域
                }
                flowPath.removeChild(node); // 删除节点本身
            }
        }

        // 重置流程
        function resetFlow() {
            if (confirm('确定要重置流程吗？所有修改将丢失！')) {
                location.reload();
            }
        }

        // 预览流程
        function previewFlow() {
            const nodes = document.querySelectorAll('.flow-node');
            let flowText = '流程预览：\n\n';

            nodes.forEach((node, index) => {
                const nodeName = node.querySelector('span:last-child').textContent;
                const isRequired = node.classList.contains('required');
                flowText += `${index + 1}. ${nodeName} ${isRequired ? '(必需)' : '(可选)'}\n`;
            });

            alert(flowText);
        }

        // 保存流程
        function saveFlow() {
            const nodes = document.querySelectorAll('.flow-node');
            const flowData = Array.from(nodes).map((node, index) => ({
                id: node.dataset.id,
                type: node.dataset.type,
                name: node.querySelector('span:last-child').textContent,
                required: node.classList.contains('required'),
                order: index + 1
            }));

            console.log('保存的流程数据:', flowData);
            alert('流程保存成功！\n\n共 ' + nodes.length + ' 个节点\n必需节点：' +
                  Array.from(nodes).filter(n => n.classList.contains('required')).length + ' 个');
        }

        // 移除投放区域的激活状态
        document.addEventListener('dragleave', function(e) {
            if (e.target.classList.contains('drop-zone')) {
                e.target.classList.remove('active');
            }
        });
    </script>
</body>
</html>