package com.grain.risk.controller;

import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * 节假日管理控制器
 */
@RestController
@RequestMapping("/api/holidays")
@CrossOrigin(origins = "*")
public class HolidayController {

    /**
     * 分页查询节假日
     */
    @GetMapping
    public Map<String, Object> getHolidays(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String year,
            @RequestParam(required = false) String holidayType,
            @RequestParam(required = false) String holidayName) {

        // 模拟数据
        List<Map<String, Object>> holidays = Arrays.asList(
            createHoliday(1L, "2024-01-01", "元旦", 1, 0, 2024, "元旦节"),
            createHoliday(2L, "2024-02-10", "春节", 1, 0, 2024, "春节假期"),
            createHoliday(3L, "2024-02-11", "春节", 1, 0, 2024, "春节假期"),
            createHoliday(4L, "2024-04-04", "清明节", 1, 0, 2024, "清明节"),
            createHoliday(5L, "2024-05-01", "劳动节", 1, 0, 2024, "劳动节"),
            createHoliday(6L, "2024-02-04", "春节调休", 2, 1, 2024, "春节调休上班")
        );

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "查询成功");

        Map<String, Object> data = new HashMap<>();
        data.put("records", holidays);
        data.put("current", page);
        data.put("pages", 1);
        data.put("total", holidays.size());

        result.put("data", data);
        return result;
    }

    private Map<String, Object> createHoliday(Long id, String date, String name, int type, int isWorkday, int year, String remark) {
        Map<String, Object> holiday = new HashMap<>();
        holiday.put("id", id);
        holiday.put("holidayDate", date);
        holiday.put("holidayName", name);
        holiday.put("holidayType", type);
        holiday.put("isWorkday", isWorkday);
        holiday.put("yearNum", year);
        holiday.put("remark", remark);
        holiday.put("createdTime", "2024-01-01 10:00:00");
        return holiday;
    }
}