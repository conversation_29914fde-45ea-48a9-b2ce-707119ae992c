<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速配置向导 - 步骤3：确认生效</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            background: rgba(255,255,255,0.2);
            height: 8px;
            border-radius: 4px;
            margin-top: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: white;
            height: 100%;
            width: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 20px;
        }
        
        .step {
            display: flex;
            align-items: center;
            color: rgba(255,255,255,0.7);
            font-size: 14px;
        }
        
        .step.active {
            color: white;
            font-weight: bold;
        }
        
        .step.completed {
            color: rgba(255,255,255,0.9);
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 12px;
        }
        
        .step.active .step-number {
            background: white;
            color: #9c27b0;
        }
        
        .step.completed .step-number {
            background: rgba(255,255,255,0.9);
            color: #9c27b0;
        }
        
        .main-content {
            padding: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }
        
        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }
        
        .config-summary {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #9c27b0;
        }
        
        .config-summary h2 {
            color: #2c3e50;
            font-size: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .summary-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }
        
        .summary-item h4 {
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .summary-item p {
            color: #6c757d;
            font-size: 13px;
        }
        
        .mobile-preview {
            background: #2c3e50;
            border-radius: 12px;
            padding: 25px;
            color: white;
        }
        
        .mobile-preview h2 {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .phone-mockup {
            background: #34495e;
            border-radius: 20px;
            padding: 20px;
            margin: 0 auto;
            max-width: 300px;
            position: relative;
        }
        
        .phone-screen {
            background: white;
            border-radius: 15px;
            padding: 20px;
            color: #2c3e50;
            min-height: 400px;
        }
        
        .form-field {
            margin-bottom: 15px;
        }
        
        .form-field label {
            display: block;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .form-field input, .form-field select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .new-field {
            background: #e8f5e8;
            border-left: 3px solid #28a745;
            padding: 10px;
            border-radius: 4px;
        }
        
        .new-badge {
            background: #28a745;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: bold;
            margin-left: 5px;
        }
        
        .flow-preview {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 25px;
        }
        
        .flow-preview h2 {
            color: #856404;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .flow-steps {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .flow-step {
            background: white;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .flow-step.new {
            border-left-color: #28a745;
            background: #f8fff9;
        }
        
        .step-info {
            display: flex;
            align-items: center;
        }
        
        .step-number-flow {
            background: #ffc107;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .step-number-flow.new {
            background: #28a745;
        }
        
        .settings-panel {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 12px;
            padding: 25px;
        }
        
        .settings-panel h2 {
            color: #1976d2;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .setting-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        
        .setting-group h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .radio-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .radio-item input[type="radio"] {
            margin: 0;
        }
        
        .radio-item label {
            font-size: 13px;
            color: #2c3e50;
            cursor: pointer;
        }
        
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input[type="checkbox"] {
            margin: 0;
        }
        
        .checkbox-item label {
            font-size: 13px;
            color: #2c3e50;
            cursor: pointer;
        }
        
        .action-buttons {
            grid-column: 1 / -1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border: none;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
            font-size: 18px;
            padding: 15px 30px;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-outline {
            background: white;
            color: #6c757d;
            border: 2px solid #6c757d;
        }
        
        .btn-outline:hover {
            background: #6c757d;
            color: white;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ 确认生效</h1>
            <p>最后一步，确认配置并生效</p>
            
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            
            <div class="step-indicator">
                <div class="step completed">
                    <span class="step-number">✓</span>
                    选择模板
                </div>
                <div class="step completed">
                    <span class="step-number">✓</span>
                    差异化配置
                </div>
                <div class="step active">
                    <span class="step-number">3</span>
                    确认生效
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <!-- 配置总览 -->
                <div class="config-summary">
                    <h2>
                        <span class="section-icon">📋</span>
                        配置总览
                    </h2>
                    
                    <div class="summary-item">
                        <h4>📍 适用库点</h4>
                        <p>朝阳库点（中型库点）</p>
                    </div>
                    
                    <div class="summary-item">
                        <h4>🔥 作业类型</h4>
                        <p>动火作业</p>
                    </div>
                    
                    <div class="summary-item">
                        <h4>📊 基础模板</h4>
                        <p>标准版模板A（5个节点，12个字段）</p>
                    </div>
                    
                    <div class="summary-item">
                        <h4>⚙️ 扩展内容</h4>
                        <p>环保审批节点、现场拍照节点、GPS定位字段、监护人手机字段、现场照片字段、审批时限调整</p>
                    </div>
                    
                    <div class="summary-item">
                        <h4>📈 预期效果</h4>
                        <p>7个节点，15个字段，审批时间1-2小时</p>
                    </div>
                </div>
                
                <!-- 移动端预览 -->
                <div class="mobile-preview">
                    <h2>
                        <span class="section-icon">📱</span>
                        移动端预览
                    </h2>
                    
                    <div class="phone-mockup">
                        <div class="phone-screen">
                            <h3 style="text-align: center; margin-bottom: 15px; font-size: 14px;">动火作业申请表</h3>
                            
                            <div class="form-field">
                                <label>作业标题 *</label>
                                <input type="text" placeholder="请输入作业标题">
                            </div>
                            
                            <div class="form-field">
                                <label>作业地点 *</label>
                                <input type="text" placeholder="请输入作业地点">
                            </div>
                            
                            <div class="form-field new-field">
                                <label>GPS定位 * <span class="new-badge">新增</span></label>
                                <input type="text" value="自动获取中..." readonly>
                            </div>
                            
                            <div class="form-field">
                                <label>计划开始时间 *</label>
                                <input type="datetime-local">
                            </div>
                            
                            <div class="form-field new-field">
                                <label>监护人手机 * <span class="new-badge">新增</span></label>
                                <input type="tel" placeholder="请输入监护人手机号">
                            </div>
                            
                            <div class="form-field">
                                <label>风险等级 *</label>
                                <select>
                                    <option>请选择风险等级</option>
                                    <option>低风险</option>
                                    <option>中风险</option>
                                    <option>高风险</option>
                                </select>
                            </div>
                            
                            <div class="form-field new-field">
                                <label>现场照片 * <span class="new-badge">新增</span></label>
                                <input type="file" accept="image/*" multiple>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧面板 -->
            <div class="right-panel">
                <!-- 流程预览 -->
                <div class="flow-preview">
                    <h2>
                        <span class="section-icon">🔀</span>
                        流程预览
                    </h2>
                    
                    <div class="flow-steps">
                        <div class="flow-step">
                            <div class="step-info">
                                <span class="step-number-flow">1</span>
                                <span>申请提交</span>
                            </div>
                        </div>
                        
                        <div class="flow-step">
                            <div class="step-info">
                                <span class="step-number-flow">2</span>
                                <span>部门审批</span>
                            </div>
                            <span style="font-size: 12px; color: #6c757d;">1小时内</span>
                        </div>
                        
                        <div class="flow-step new">
                            <div class="step-info">
                                <span class="step-number-flow new">3</span>
                                <span>环保审批</span>
                            </div>
                            <span class="new-badge">新增</span>
                        </div>
                        
                        <div class="flow-step">
                            <div class="step-info">
                                <span class="step-number-flow">4</span>
                                <span>安全检查</span>
                            </div>
                        </div>
                        
                        <div class="flow-step new">
                            <div class="step-info">
                                <span class="step-number-flow new">5</span>
                                <span>现场拍照</span>
                            </div>
                            <span class="new-badge">新增</span>
                        </div>
                        
                        <div class="flow-step">
                            <div class="step-info">
                                <span class="step-number-flow">6</span>
                                <span>开始作业</span>
                            </div>
                        </div>
                        
                        <div class="flow-step">
                            <div class="step-info">
                                <span class="step-number-flow">7</span>
                                <span>作业验收</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 生效设置 -->
                <div class="settings-panel">
                    <h2>
                        <span class="section-icon">⚙️</span>
                        生效设置
                    </h2>
                    
                    <div class="setting-group">
                        <h4>生效时间</h4>
                        <div class="radio-group">
                            <div class="radio-item">
                                <input type="radio" id="immediate" name="timing" checked>
                                <label for="immediate">⚡ 立即生效（配置后立即可用）</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="scheduled" name="timing">
                                <label for="scheduled">📅 定时生效（指定时间生效）</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-group">
                        <h4>配置方式</h4>
                        <div class="radio-group">
                            <div class="radio-item">
                                <input type="radio" id="replace" name="method" checked>
                                <label for="replace">🔄 替换现有配置（覆盖当前配置）</label>
                            </div>
                            <div class="radio-item">
                                <input type="radio" id="new" name="method">
                                <label for="new">➕ 新增配置（保留现有配置）</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-group">
                        <h4>通知设置</h4>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="notify-admin" checked>
                                <label for="notify-admin">📧 通知库点管理员</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="notify-safety" checked>
                                <label for="notify-safety">📧 通知安全主管</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="notify-all">
                                <label for="notify-all">📧 通知所有用户</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-group">
                        <h4>培训支持</h4>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="send-guide" checked>
                                <label for="send-guide">📚 发送操作指南</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="training">
                                <label for="training">📚 安排培训课程</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="差异化配置.html" class="btn btn-secondary">
                    ← 上一步：修改配置
                </a>
                
                <div style="display: flex; gap: 15px;">
                    <button class="btn btn-outline">
                        💾 保存为草稿
                    </button>
                    <button class="btn btn-success" onclick="confirmConfig()">
                        ✅ 确认并生效
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function confirmConfig() {
            // 显示确认对话框
            if (confirm('确认要生效此配置吗？配置生效后将立即应用到朝阳库点的动火作业流程。')) {
                // 模拟配置生效过程
                alert('配置已成功生效！\n\n✅ 动火作业流程已更新\n📧 相关人员已收到通知\n📚 操作指南已发送\n\n您可以在配置状态页面查看详细信息。');
                
                // 跳转到成功页面或主界面
                window.location.href = '配置功能主界面.html';
            }
        }
    </script>
</body>
</html>
