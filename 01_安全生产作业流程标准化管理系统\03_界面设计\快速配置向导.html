<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速配置向导 - 步骤1：选择模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .progress-bar {
            background: rgba(255,255,255,0.2);
            height: 8px;
            border-radius: 4px;
            margin-top: 20px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: white;
            height: 100%;
            width: 33.33%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 20px;
        }
        
        .step {
            display: flex;
            align-items: center;
            color: rgba(255,255,255,0.7);
            font-size: 14px;
        }
        
        .step.active {
            color: white;
            font-weight: bold;
        }
        
        .step-number {
            background: rgba(255,255,255,0.2);
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            font-size: 12px;
        }
        
        .step.active .step-number {
            background: white;
            color: #28a745;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #2c3e50;
            font-size: 20px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .option-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .option-card {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .option-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .option-card.selected {
            border-color: #28a745;
            background: #f8fff9;
            box-shadow: 0 8px 25px rgba(40,167,69,0.2);
        }
        
        .option-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .option-card h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        
        .option-card p {
            font-size: 14px;
            color: #6c757d;
            line-height: 1.4;
        }
        
        .recommendation {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
        }
        
        .recommendation h3 {
            color: #1976d2;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .recommendation-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .recommendation-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .recommendation-info {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .recommendation-info h4 {
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .recommendation-info p {
            color: #6c757d;
            font-size: 14px;
        }
        
        .template-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .template-preview h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .flow-steps {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .flow-step {
            background: #007bff;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .flow-arrow {
            color: #6c757d;
            font-size: 16px;
        }
        
        .action-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }
        
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
            border: none;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
            border: none;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .info-badge {
            background: #17a2b8;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 快速配置向导</h1>
            <p>3步完成配置，让您的作业流程快速上线</p>
            
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            
            <div class="step-indicator">
                <div class="step active">
                    <span class="step-number">1</span>
                    选择模板
                </div>
                <div class="step">
                    <span class="step-number">2</span>
                    差异化配置
                </div>
                <div class="step">
                    <span class="step-number">3</span>
                    确认生效
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <!-- 作业类型选择 -->
            <div class="section">
                <h2>
                    <span class="section-icon">🔥</span>
                    选择作业类型
                </h2>
                <div class="option-grid">
                    <div class="option-card selected" onclick="selectOption(this)">
                        <span class="option-icon">🔥</span>
                        <h3>动火作业</h3>
                        <p>焊接、切割等明火作业</p>
                    </div>
                    <div class="option-card" onclick="selectOption(this)">
                        <span class="option-icon">🏠</span>
                        <h3>有限空间作业</h3>
                        <p>密闭空间内作业</p>
                    </div>
                    <div class="option-card" onclick="selectOption(this)">
                        <span class="option-icon">⬆️</span>
                        <h3>高处作业</h3>
                        <p>2米以上高度作业</p>
                    </div>
                    <div class="option-card" onclick="selectOption(this)">
                        <span class="option-icon">🔧</span>
                        <h3>设备检修</h3>
                        <p>设备维护检修作业</p>
                    </div>
                </div>
            </div>
            
            <!-- 库点规模选择 -->
            <div class="section">
                <h2>
                    <span class="section-icon">🏭</span>
                    选择库点规模
                </h2>
                <div class="option-grid">
                    <div class="option-card" onclick="selectScale(this)">
                        <span class="option-icon">🏭</span>
                        <h3>大型库点</h3>
                        <p>> 5万吨<br><span class="info-badge">推荐：严格版模板</span></p>
                    </div>
                    <div class="option-card selected" onclick="selectScale(this)">
                        <span class="option-icon">🏢</span>
                        <h3>中型库点</h3>
                        <p>1-5万吨<br><span class="info-badge">推荐：标准版模板</span></p>
                    </div>
                    <div class="option-card" onclick="selectScale(this)">
                        <span class="option-icon">🏪</span>
                        <h3>小型库点</h3>
                        <p>< 1万吨<br><span class="info-badge">推荐：简化版模板</span></p>
                    </div>
                </div>
            </div>
            
            <!-- 智能推荐结果 -->
            <div class="recommendation">
                <h3>
                    <span class="recommendation-icon">⚡</span>
                    智能推荐结果
                </h3>
                
                <div class="recommendation-content">
                    <div class="recommendation-info">
                        <h4>📋 推荐模板</h4>
                        <p>动火作业 - 标准版模板A</p>
                    </div>
                    <div class="recommendation-info">
                        <h4>📊 流程节点</h4>
                        <p>5个标准节点</p>
                    </div>
                    <div class="recommendation-info">
                        <h4>📝 表单字段</h4>
                        <p>12个基础字段</p>
                    </div>
                    <div class="recommendation-info">
                        <h4>⏱️ 预计审批时间</h4>
                        <p>2-4小时</p>
                    </div>
                </div>
                
                <div class="template-preview">
                    <h4>🔀 流程预览</h4>
                    <div class="flow-steps">
                        <div class="flow-step">申请提交</div>
                        <span class="flow-arrow">→</span>
                        <div class="flow-step">部门审批</div>
                        <span class="flow-arrow">→</span>
                        <div class="flow-step">安全检查</div>
                        <span class="flow-arrow">→</span>
                        <div class="flow-step">开始作业</div>
                        <span class="flow-arrow">→</span>
                        <div class="flow-step">作业验收</div>
                    </div>
                </div>
                
                <div style="margin-top: 15px; padding: 10px; background: rgba(40,167,69,0.1); border-radius: 6px;">
                    <strong>👥 涉及角色：</strong> 申请人、审批人、安全员、验收员
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="配置功能主界面.html" class="btn btn-secondary">
                    ← 返回主界面
                </a>
                
                <div style="display: flex; gap: 15px;">
                    <button class="btn btn-secondary">
                        👀 查看详细信息
                    </button>
                    <button class="btn btn-primary">
                        🔄 选择其他模板
                    </button>
                    <a href="差异化配置.html" class="btn btn-success">
                        下一步：差异化配置 →
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function selectOption(element) {
            // 移除同组其他选项的选中状态
            const parent = element.parentNode;
            const siblings = parent.querySelectorAll('.option-card');
            siblings.forEach(card => card.classList.remove('selected'));
            
            // 添加当前选项的选中状态
            element.classList.add('selected');
        }
        
        function selectScale(element) {
            // 移除同组其他选项的选中状态
            const parent = element.parentNode;
            const siblings = parent.querySelectorAll('.option-card');
            siblings.forEach(card => card.classList.remove('selected'));
            
            // 添加当前选项的选中状态
            element.classList.add('selected');
            
            // 更新推荐结果（这里可以添加更复杂的逻辑）
            updateRecommendation();
        }
        
        function updateRecommendation() {
            // 这里可以根据选择的库点规模更新推荐模板
            console.log('更新推荐结果');
        }
    </script>
</body>
</html>
