<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NFC验证测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 4px solid #007bff;
            transition: transform 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .test-card.success {
            border-left-color: #28a745;
        }
        
        .test-card.danger {
            border-left-color: #dc3545;
        }
        
        .test-card.warning {
            border-left-color: #ffc107;
        }
        
        .test-card.secondary {
            border-left-color: #6c757d;
        }
        
        .test-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .test-icon {
            font-size: 24px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .test-desc {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        
        .test-btn {
            width: 100%;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            text-align: center;
            color: white;
        }
        
        .test-btn.success {
            background: #28a745;
        }
        
        .test-btn.success:hover {
            background: #1e7e34;
        }
        
        .test-btn.danger {
            background: #dc3545;
        }
        
        .test-btn.danger:hover {
            background: #c82333;
        }
        
        .test-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .test-btn.warning:hover {
            background: #e0a800;
        }
        
        .test-btn.secondary {
            background: #6c757d;
        }
        
        .test-btn.secondary:hover {
            background: #5a6268;
        }
        
        .test-btn.primary {
            background: #007bff;
        }
        
        .test-btn.primary:hover {
            background: #0056b3;
        }
        
        .instructions {
            background: #e3f2fd;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }
        
        .instructions h2 {
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .instructions p {
            color: #1976d2;
            line-height: 1.6;
            margin-bottom: 10px;
        }
        
        .instructions ul {
            color: #1976d2;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 NFC验证测试页面</h1>
            <p>测试不同的验证场景和失败情况</p>
        </div>
        
        <div class="content">
            <div class="instructions">
                <h2>📋 使用说明</h2>
                <p>点击下方按钮可以测试不同的NFC验证场景：</p>
                <ul>
                    <li><strong>验证成功</strong>：显示正版产品的完整信息</li>
                    <li><strong>假冒产品</strong>：显示假冒产品警告和举报功能</li>
                    <li><strong>标签过期</strong>：显示过期提示和更新申请</li>
                    <li><strong>标签损坏</strong>：显示读取异常和故障排除</li>
                    <li><strong>网络错误</strong>：显示连接失败和离线验证</li>
                    <li><strong>未知错误</strong>：显示系统异常和技术支持</li>
                </ul>
            </div>
            
            <div class="test-grid">
                <!-- 验证成功 -->
                <div class="test-card success">
                    <div class="test-header">
                        <span class="test-icon">✅</span>
                        <span class="test-title">验证成功</span>
                    </div>
                    <div class="test-desc">
                        模拟正版产品验证成功的场景，显示完整的产品信息、认证证书和专利获奖视频。
                    </div>
                    <a href="APP端验证页面.html?test=success" class="test-btn success" target="_blank">
                        测试验证成功
                    </a>
                </div>
                
                <!-- 假冒产品 -->
                <div class="test-card danger">
                    <div class="test-header">
                        <span class="test-icon">⚠️</span>
                        <span class="test-title">假冒产品</span>
                    </div>
                    <div class="test-desc">
                        模拟检测到假冒产品的场景，显示安全风险提示、识别正品指南和举报功能。
                    </div>
                    <a href="APP端验证页面.html?test=fake" class="test-btn danger" target="_blank">
                        测试假冒产品
                    </a>
                </div>
                
                <!-- 标签过期 -->
                <div class="test-card warning">
                    <div class="test-header">
                        <span class="test-icon">⏰</span>
                        <span class="test-title">标签过期</span>
                    </div>
                    <div class="test-desc">
                        模拟产品标签已过期的场景，显示过期信息、处理建议和标签更新申请。
                    </div>
                    <a href="APP端验证页面.html?test=expired" class="test-btn warning" target="_blank">
                        测试标签过期
                    </a>
                </div>
                
                <!-- 标签损坏 -->
                <div class="test-card secondary">
                    <div class="test-header">
                        <span class="test-icon">📱</span>
                        <span class="test-title">标签损坏</span>
                    </div>
                    <div class="test-desc">
                        模拟NFC标签读取异常的场景，显示故障排除步骤、可能原因和技术支持。
                    </div>
                    <a href="APP端验证页面.html?test=damaged" class="test-btn secondary" target="_blank">
                        测试标签损坏
                    </a>
                </div>
                
                <!-- 网络错误 -->
                <div class="test-card primary">
                    <div class="test-header">
                        <span class="test-icon">📶</span>
                        <span class="test-title">网络错误</span>
                    </div>
                    <div class="test-desc">
                        模拟网络连接失败的场景，显示网络检查步骤、离线验证提示和人工验证。
                    </div>
                    <a href="APP端验证页面.html?test=network" class="test-btn primary" target="_blank">
                        测试网络错误
                    </a>
                </div>
                
                <!-- 未知错误 -->
                <div class="test-card warning">
                    <div class="test-header">
                        <span class="test-icon">❓</span>
                        <span class="test-title">未知错误</span>
                    </div>
                    <div class="test-desc">
                        模拟系统未知错误的场景，显示错误详情、错误报告和技术支持联系方式。
                    </div>
                    <a href="APP端验证页面.html?test=unknown" class="test-btn warning" target="_blank">
                        测试未知错误
                    </a>
                </div>
            </div>
            
            <div style="margin-top: 40px; text-align: center; color: #6c757d;">
                <p>💡 提示：每个测试场景都会在新窗口中打开，方便对比查看效果</p>
                <p style="margin-top: 10px;">
                    <a href="管理端主界面.html" style="color: #007bff; text-decoration: none;">
                        🔙 返回管理端主界面
                    </a>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
